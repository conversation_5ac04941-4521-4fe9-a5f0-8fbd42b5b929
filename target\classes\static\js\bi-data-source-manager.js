/**
 * BI仪表盘数据源管理器
 * 统一管理所有组件的数据源功能，包括监控项数据源、静态数据、多数据源
 */

class BiDataSourceManager {
    constructor() {
        this.supportedTypes = ['dataItem', 'static', 'multiData', 'externalData'];
        this.componentConfigs = this.initComponentConfigs();
        // 组件配置上下文管理
        this.componentContexts = new Map();
        // 配置验证缓存
        this.validationCache = new Map();
    }

    /**
     * 初始化组件配置映射
     */
    initComponentConfigs() {
        return {
            'pie-chart': {
                supportedDataSources: ['static', 'multiData', 'externalData'],
                multiDataConfig: {
                    containerId: 'pieDataSourceList',
                    idPrefix: 'pieData',
                    labelPlaceholder: '产品A',
                    dataFormat: 'pie'
                },
                multiExternalDataConfig: {
                    containerId: 'pieExternalDataSourceList',
                    idPrefix: 'pieExternalData',
                    labelPlaceholder: '数据集A',
                    dataFormat: 'pie'
                }
            },
            'bar-chart': {
                supportedDataSources: ['dataItem', 'static', 'multiData', 'externalData'],
                multiDataConfig: {
                    containerId: 'barDataSourceList',
                    idPrefix: 'barData',
                    labelPlaceholder: '系列A',
                    dataFormat: 'chart'
                },
                multiExternalDataConfig: {
                    containerId: 'barExternalDataSourceList',
                    idPrefix: 'barExternalData',
                    labelPlaceholder: '数据集A',
                    dataFormat: 'chart'
                }
            },
            'horizontal-bar-chart': {
                supportedDataSources: ['dataItem', 'static', 'multiData', 'externalData'],
                multiDataConfig: {
                    containerId: 'barDataSourceList',
                    idPrefix: 'barData',
                    labelPlaceholder: '系列A',
                    dataFormat: 'chart'
                },
                multiExternalDataConfig: {
                    containerId: 'horizontalBarExternalDataSourceList',
                    idPrefix: 'horizontalBarExternalData',
                    labelPlaceholder: '数据集A',
                    dataFormat: 'chart'
                }
            },
            'line-chart': {
                supportedDataSources: ['dataItem', 'static', 'externalData'],
                multiDataConfig: null,
                multiExternalDataConfig: {
                    containerId: 'lineExternalDataSourceList',
                    idPrefix: 'lineExternalData',
                    labelPlaceholder: '数据集A',
                    dataFormat: 'line'
                }
            },
            'gauge-chart': {
                supportedDataSources: ['dataItem', 'static', 'externalData'],
                multiDataConfig: null,
                specialHandling: 'singleValue'
            },
            'water-chart': {
                supportedDataSources: ['dataItem', 'static', 'externalData'],
                multiDataConfig: null,
                specialHandling: 'singleValue'
            },
            'column-percentage-chart': {
                supportedDataSources: ['dataItem', 'static', 'externalData'],
                multiDataConfig: null,
                specialHandling: 'singleValue'
            },
            'text-label': {
                supportedDataSources: ['dataItem', 'static', 'externalData'],
                multiDataConfig: null,
                specialHandling: 'singleValue'
            },
            'multi-line-chart': {
                supportedDataSources: ['externalData'],
                multiExternalDataConfig: {
                    containerId: 'multiLineExternalDataSourceList',
                    idPrefix: 'multiLineExternalData',
                    labelPlaceholder: '数据集A',
                    dataFormat: 'multiLine'
                }
            },
            'data-table': {
                supportedDataSources: ['dataItem', 'static', 'multiData', 'externalData'],
                multiDataConfig: {
                    containerId: 'barDataSourceList',
                    idPrefix: 'barData',
                    labelPlaceholder: '监控项A',
                    dataFormat: 'table'
                },
                multiExternalDataConfig: {
                    containerId: 'tableExternalDataSourceList',
                    idPrefix: 'tableExternalData',
                    labelPlaceholder: '数据集A',
                    dataFormat: 'table'
                }
            },
            'status-indicator': {
                supportedDataSources: ['dataItem'],
                multiDataConfig: null,
                multiExternalDataConfig: null
            }
        };
    }

    /**
     * 获取组件支持的数据源类型
     */
    getSupportedDataSources(componentType) {
        const config = this.componentConfigs[componentType];
        return config ? config.supportedDataSources : ['dataItem', 'static'];
    }

    /**
     * 获取多数据源配置
     */
    getMultiDataSourceConfig(componentType) {
        const config = this.componentConfigs[componentType];
        return config ? config.multiDataConfig : null;
    }

    /**
     * 获取多外部数据源配置
     */
    getMultiExternalDataSourceConfig(componentType) {
        const config = this.componentConfigs[componentType];
        return config ? config.multiExternalDataConfig : null;
    }

    /**
     * 创建组件配置上下文
     * 为每个组件创建独立的配置作用域，防止配置污染
     */
    createComponentContext(widget) {
        if (!widget || !widget.id) {
            console.warn('创建组件上下文失败：无效的组件对象');
            return null;
        }

        const contextId = `${widget.id}_${widget.type || widget.widgetType || 'unknown'}`;

        // 如果上下文已存在，先清理
        if (this.componentContexts.has(contextId)) {
            this.clearComponentContext(widget);
        }

        const context = {
            widgetId: widget.id,
            widgetType: widget.type || widget.widgetType,
            createdAt: new Date().toISOString(),
            domElements: new Map(),
            configState: {},
            isolated: true
        };

        this.componentContexts.set(contextId, context);
        console.log(`为组件 ${widget.id} (${context.widgetType}) 创建配置上下文`);

        return context;
    }

    /**
     * 清理组件配置上下文
     */
    clearComponentContext(widget) {
        if (!widget || !widget.id) {
            console.warn('清理组件上下文失败：无效的组件对象');
            return false;
        }

        const contextId = `${widget.id}_${widget.type || widget.widgetType || 'unknown'}`;

        if (this.componentContexts.has(contextId)) {
            const context = this.componentContexts.get(contextId);

            // 清理DOM元素引用
            context.domElements.clear();

            // 清理配置状态
            context.configState = {};

            // 移除上下文
            this.componentContexts.delete(contextId);

            console.log(`已清理组件 ${widget.id} 的配置上下文`);
            return true;
        }

        return false;
    }

    /**
     * 获取组件特定的DOM元素
     * 确保每个组件访问的是正确的DOM元素，避免配置污染
     */
    getComponentSpecificElement(widget, elementId) {
        if (!widget || !elementId) {
            console.warn('获取组件特定元素失败：参数无效');
            return null;
        }

        const contextId = `${widget.id}_${widget.type || widget.widgetType || 'unknown'}`;
        let context = this.componentContexts.get(contextId);

        // 如果上下文不存在，创建一个
        if (!context) {
            context = this.createComponentContext(widget);
        }

        // 检查缓存的DOM元素
        if (context.domElements.has(elementId)) {
            const cachedElement = context.domElements.get(elementId);
            // 验证元素是否仍然存在于DOM中
            if (document.contains(cachedElement)) {
                return cachedElement;
            } else {
                // 元素已从DOM中移除，清理缓存
                context.domElements.delete(elementId);
            }
        }

        // 获取DOM元素
        const element = document.getElementById(elementId);
        if (element) {
            // 缓存元素引用
            context.domElements.set(elementId, element);
            console.log(`为组件 ${widget.id} 缓存DOM元素: ${elementId}`);
        }

        return element;
    }

    /**
     * 检查组件是否支持多数据源
     */
    supportsMultiDataSource(componentType) {
        const config = this.componentConfigs[componentType];
        return config && config.multiDataConfig !== null;
    }

    /**
     * 验证数据源配置
     */
    validateDataSourceConfig(widget, config) {
        if (!widget || !config) {
            return {
                isValid: false,
                errors: ['组件或配置对象无效'],
                warnings: []
            };
        }

        const validation = {
            isValid: true,
            errors: [],
            warnings: []
        };

        // 检查数据源类型
        if (!config.dataSourceType || !this.supportedTypes.includes(config.dataSourceType)) {
            validation.errors.push(`无效的数据源类型: ${config.dataSourceType}`);
            validation.isValid = false;
        }

        // 检查组件是否支持该数据源类型
        const supportedTypes = this.getSupportedDataSources(widget.type || widget.widgetType);
        if (config.dataSourceType && !supportedTypes.includes(config.dataSourceType)) {
            validation.warnings.push(`组件 ${widget.type || widget.widgetType} 可能不支持数据源类型 ${config.dataSourceType}`);
        }

        // 根据数据源类型进行具体验证
        switch (config.dataSourceType) {
            case 'dataItem':
                if (!config.deviceId) validation.errors.push('监控项数据源缺少设备ID');
                if (!config.dataItemId) validation.errors.push('监控项数据源缺少数据项ID');
                break;
            case 'static':
                if (!config.staticLabels && !config.staticValues) {
                    validation.warnings.push('静态数据源缺少标签或数值配置');
                }
                break;
            case 'externalData':
                if (!config.dataSetId && !config.dataSets) {
                    validation.errors.push('外部数据源缺少数据集配置');
                }
                break;
        }

        if (validation.errors.length > 0) {
            validation.isValid = false;
        }

        return validation;
    }

    /**
     * 修复数据源配置缺陷
     */
    repairDataSourceConfig(widget, config) {
        if (!widget || !config) {
            console.warn('修复配置失败：参数无效');
            return config;
        }

        const repairedConfig = { ...config };

        // 设置默认数据源类型
        if (!repairedConfig.dataSourceType) {
            const supportedTypes = this.getSupportedDataSources(widget.type || widget.widgetType);
            repairedConfig.dataSourceType = supportedTypes[0] || 'dataItem';
            console.log(`为组件 ${widget.id} 设置默认数据源类型: ${repairedConfig.dataSourceType}`);
        }

        // 根据数据源类型设置默认值
        const defaults = this.getDataSourceConfigDefaults(widget);
        for (const key in defaults) {
            if (repairedConfig[key] === undefined || repairedConfig[key] === null) {
                repairedConfig[key] = defaults[key];
            }
        }

        return repairedConfig;
    }

    /**
     * 获取数据源配置默认值
     */
    getDataSourceConfigDefaults(widget) {
        const widgetType = widget.type || widget.widgetType;
        const supportedTypes = this.getSupportedDataSources(widgetType);
        const defaultType = supportedTypes[0] || 'dataItem';

        const defaults = {
            dataSourceType: defaultType,
            refreshInterval: 5
        };

        // 根据数据源类型添加特定默认值
        switch (defaultType) {
            case 'dataItem':
                defaults.dataMode = 'realtime';
                defaults.historyCount = 10;
                defaults.timeFormat = 'HH:mm:ss';
                break;
            case 'static':
                defaults.staticLabels = '';
                defaults.staticValues = '';
                break;
            case 'externalData':
                defaults.dataSetId = null;
                defaults.labelField = null;
                defaults.valueField = null;
                break;
        }

        return defaults;
    }

    /**
     * 统一的数据源配置收集（增强版，包含验证和修复）
     */
    collectDataSourceConfig(widget) {
        // 创建或获取组件上下文
        const context = this.createComponentContext(widget);

        const dataSourceTypeElement = this.getComponentSpecificElement(widget, 'dataSourceType');
        const dataSourceType = dataSourceTypeElement ? dataSourceTypeElement.value : 'dataItem';

        let config = { dataSourceType };

        try {
            switch (dataSourceType) {
                case 'dataItem':
                    config = this.collectDataItemConfig(config, widget);
                    break;
                case 'static':
                    config = this.collectStaticDataConfig(config, widget);
                    break;
                case 'multiData':
                    config = this.collectMultiDataConfig(config, widget);
                    break;
                case 'externalData':
                    config = this.collectExternalDataConfig(config, widget);
                    break;
                default:
                    console.warn(`未知的数据源类型: ${dataSourceType}`);
                    break;
            }

            // 验证配置
            const validation = this.validateDataSourceConfig(widget, config);
            if (!validation.isValid) {
                console.warn(`组件 ${widget.id} 数据源配置验证失败:`, validation.errors);
                // 尝试修复配置
                config = this.repairDataSourceConfig(widget, config);
            }

            if (validation.warnings.length > 0) {
                console.warn(`组件 ${widget.id} 数据源配置警告:`, validation.warnings);
            }

            // 缓存配置到上下文
            if (context) {
                context.configState = { ...config };
            }

            console.log(`组件 ${widget.id} 数据源配置收集完成:`, config);
            return config;

        } catch (error) {
            console.error(`收集组件 ${widget.id} 数据源配置时发生错误:`, error);
            // 返回修复后的默认配置
            return this.repairDataSourceConfig(widget, config);
        }
    }

    /**
     * 收集监控项数据源配置（增强版，支持组件上下文）
     */
    collectDataItemConfig(config, widget = null) {
        console.log('=== 收集监控项数据源配置 ===');
        console.log('Widget:', widget ? `${widget.id} (${widget.type})` : 'null');

        // 对于状态指示器组件，直接使用标准元素ID
        const isStatusIndicator = widget && (widget.type === 'status-indicator' || widget.widgetType === 'status-indicator');

        const getElement = (id) => {
            if (isStatusIndicator) {
                // 状态指示器直接使用标准元素ID
                const element = document.getElementById(id);
                console.log(`状态指示器获取元素 ${id}:`, element ? `找到，值=${element.value}` : '未找到');
                return element;
            } else {
                // 其他组件使用组件特定的元素获取方法
                return widget ? this.getComponentSpecificElement(widget, id) : document.getElementById(id);
            }
        };

        const deviceSelect = getElement('deviceSelect');
        const dataItemSelect = getElement('dataItemSelect');
        const dataModeSelect = getElement('dataMode');
        const historyCountInput = getElement('historyCount');
        const refreshIntervalInput = getElement('refreshInterval');
        const timeFormatSelect = getElement('timeFormat');

        const deviceId = deviceSelect ? deviceSelect.value : '';
        const dataItemId = dataItemSelect ? dataItemSelect.value : '';
        const dataMode = dataModeSelect ? dataModeSelect.value : 'realtime';
        const historyCount = historyCountInput ? parseInt(historyCountInput.value) || 10 : 10;
        const refreshInterval = refreshIntervalInput ? parseInt(refreshIntervalInput.value) || 5 : 5;
        const timeFormat = timeFormatSelect ? timeFormatSelect.value : 'HH:mm:ss';

        console.log('收集到的配置:', {
            deviceId,
            dataItemId,
            dataMode,
            historyCount,
            refreshInterval,
            timeFormat
        });
        console.log('=== 监控项数据源配置收集完成 ===');

        return {
            ...config,
            deviceId,
            dataItemId,
            dataMode,
            historyCount,
            refreshInterval,
            timeFormat
        };
    }

    /**
     * 收集静态数据配置（增强版，支持组件上下文）
     */
    collectStaticDataConfig(config, widget = null) {
        // 使用组件特定的元素获取方法，如果提供了widget参数
        const getElement = (id) => {
            return widget ? this.getComponentSpecificElement(widget, id) : document.getElementById(id);
        };

        const staticLabelsElement = getElement('staticLabels');
        const staticValuesElement = getElement('staticValues');

        return {
            ...config,
            staticLabels: staticLabelsElement ? staticLabelsElement.value : '',
            staticValues: staticValuesElement ? staticValuesElement.value : ''
        };
    }

    /**
     * 收集多数据源配置
     */
    collectMultiDataConfig(config, widget) {
        const multiDataConfig = this.getMultiDataSourceConfig(widget.type);
        if (!multiDataConfig) {
            console.warn('组件不支持多数据源:', widget.type);
            return config;
        }

        const countElementId = widget.type === 'pie-chart' ? 'pieDataSourceCount' : 'barDataSourceCount';
        const countElement = document.getElementById(countElementId);
        const count = countElement ? parseInt(countElement.value) : 0;
        const refreshInterval = parseInt(document.getElementById('refreshInterval').value) || 5;

        const multiDataSources = [];
        for (let i = 0; i < count; i++) {
            const label = document.getElementById(`${multiDataConfig.idPrefix}Label_${i}`);
            const device = document.getElementById(`${multiDataConfig.idPrefix}Device_${i}`);
            const dataItem = document.getElementById(`${multiDataConfig.idPrefix}Item_${i}`);

            if (label && device && dataItem && device.value && dataItem.value) {
                multiDataSources.push({
                    label: label.value || `数据源${i + 1}`,
                    deviceId: device.value,
                    dataItemId: dataItem.value
                });
            }
        }

        return {
            ...config,
            multiDataSources,
            refreshInterval
        };
    }

    /**
     * 统一的数据源配置恢复
     */
    restoreDataSourceConfig(widget, dataSourceConfig) {
        const dataSourceType = dataSourceConfig.dataSourceType || 'dataItem';

        console.log('恢复数据源配置，组件信息:', {
            id: widget?.id,
            type: widget?.type,
            widgetType: widget?.widgetType
        });

        // 设置数据源类型
        const dataSourceTypeSelect = document.getElementById('dataSourceType');
        if (dataSourceTypeSelect) {
            dataSourceTypeSelect.value = dataSourceType;
        }

        // 根据类型恢复配置
        switch (dataSourceType) {
            case 'dataItem':
                this.restoreDataItemConfig(dataSourceConfig);
                break;
            case 'static':
                this.restoreStaticDataConfig(dataSourceConfig);
                break;
            case 'multiData':
                this.restoreMultiDataConfig(widget, dataSourceConfig);
                break;
            case 'externalData':
                this.restoreExternalDataConfig(widget, dataSourceConfig);
                break;
        }

        // 触发数据源类型变化事件以显示正确的配置界面
        // 但在属性面板加载期间跳过，避免重新加载设备列表导致选择丢失
        if (typeof onDataSourceTypeChange === 'function' && !window.isLoadingPropertyPanel) {
            onDataSourceTypeChange();
        } else if (window.isLoadingPropertyPanel) {
            console.log('属性面板加载中，跳过onDataSourceTypeChange调用以保护设备选择');
        }
    }

    /**
     * 恢复监控项数据源配置（优化版，与主要恢复逻辑协调）
     */
    restoreDataItemConfig(dataSourceConfig) {
        // 注意：设备选择的恢复由主要的updatePropertyPanel函数处理
        // 这里主要处理其他配置项的恢复

        // 恢复监控项选择（如果设备已经选择）
        if (dataSourceConfig.dataItemId) {
            const dataItemSelect = document.getElementById('dataItemSelect');
            if (dataItemSelect) {
                // 检查是否需要等待监控项加载
                const deviceSelect = document.getElementById('deviceSelect');
                if (deviceSelect && deviceSelect.value === dataSourceConfig.deviceId) {
                    // 设备已选择，直接设置监控项
                    dataItemSelect.value = dataSourceConfig.dataItemId;
                    console.log('BiDataSourceManager: 恢复监控项选择:', dataSourceConfig.dataItemId);
                }
            }
        }

        // 恢复其他配置
        if (dataSourceConfig.dataMode) {
            const dataModeSelect = document.getElementById('dataMode');
            if (dataModeSelect) dataModeSelect.value = dataSourceConfig.dataMode;
        }

        if (dataSourceConfig.historyCount) {
            const historyCountInput = document.getElementById('historyCount');
            if (historyCountInput) historyCountInput.value = dataSourceConfig.historyCount;
        }

        if (dataSourceConfig.refreshInterval) {
            const refreshIntervalInput = document.getElementById('refreshInterval');
            if (refreshIntervalInput) refreshIntervalInput.value = dataSourceConfig.refreshInterval;
        }

        if (dataSourceConfig.timeFormat) {
            const timeFormatSelect = document.getElementById('timeFormat');
            if (timeFormatSelect) timeFormatSelect.value = dataSourceConfig.timeFormat;
        }
    }

    /**
     * 恢复静态数据配置
     */
    restoreStaticDataConfig(dataSourceConfig) {
        if (dataSourceConfig.staticLabels) {
            const staticLabels = document.getElementById('staticLabels');
            if (staticLabels) staticLabels.value = dataSourceConfig.staticLabels;
        }

        if (dataSourceConfig.staticValues) {
            const staticValues = document.getElementById('staticValues');
            if (staticValues) staticValues.value = dataSourceConfig.staticValues;
        }
    }

    /**
     * 恢复多数据源配置
     */
    restoreMultiDataConfig(widget, dataSourceConfig) {
        if (!dataSourceConfig.multiDataSources || !Array.isArray(dataSourceConfig.multiDataSources)) {
            return;
        }

        const count = dataSourceConfig.multiDataSources.length;
        if (count === 0) return;

        // 恢复刷新间隔配置
        if (dataSourceConfig.refreshInterval) {
            const refreshIntervalInput = document.getElementById('refreshInterval');
            if (refreshIntervalInput) refreshIntervalInput.value = dataSourceConfig.refreshInterval;
        }

        // 根据组件类型设置数据源数量和生成配置界面
        if (widget.type === 'pie-chart') {
            const pieDataSourceCount = document.getElementById('pieDataSourceCount');
            if (pieDataSourceCount) {
                pieDataSourceCount.value = count;
                if (typeof generatePieDataSourceConfig === 'function') {
                    generatePieDataSourceConfig(count, false);
                }
            }
        } else if (this.supportsMultiDataSource(widget.type)) {
            const barDataSourceCount = document.getElementById('barDataSourceCount');
            if (barDataSourceCount) {
                barDataSourceCount.value = count;
                if (typeof generateMultiDataSourceConfig === 'function') {
                    generateMultiDataSourceConfig(widget.type, count, false);
                }
            }
        }

        // 恢复每个数据源的配置
        setTimeout(() => {
            const multiDataConfig = this.getMultiDataSourceConfig(widget.type);
            if (multiDataConfig) {
                dataSourceConfig.multiDataSources.forEach((source, index) => {
                    const labelInput = document.getElementById(`${multiDataConfig.idPrefix}Label_${index}`);
                    const deviceSelect = document.getElementById(`${multiDataConfig.idPrefix}Device_${index}`);
                    const dataItemSelect = document.getElementById(`${multiDataConfig.idPrefix}Item_${index}`);

                    if (labelInput) labelInput.value = source.label || '';
                    if (deviceSelect) deviceSelect.value = source.deviceId || '';

                    // 如果有设备ID，加载监控项
                    if (source.deviceId && dataItemSelect && typeof loadDataItemsForMultiDataSource === 'function') {
                        loadDataItemsForMultiDataSource(widget.type, source.deviceId, index).then(() => {
                            if (dataItemSelect) dataItemSelect.value = source.dataItemId || '';
                        });
                    }
                });
            }
        }, 200);
    }

    /**
     * 一键式组件数据源配置
     * 简化的配置接口，适用于常见场景
     */
    configureComponentDataSource(widget, options = {}) {
        if (!widget) {
            console.error('配置组件数据源失败：组件对象无效');
            return false;
        }

        try {
            // 创建组件上下文
            this.createComponentContext(widget);

            // 设置默认选项
            const defaultOptions = {
                dataSourceType: 'dataItem',
                autoApply: true,
                validateConfig: true,
                repairConfig: true
            };

            const finalOptions = { ...defaultOptions, ...options };

            // 根据选项设置数据源类型
            const dataSourceTypeElement = this.getComponentSpecificElement(widget, 'dataSourceType');
            if (dataSourceTypeElement && finalOptions.dataSourceType) {
                dataSourceTypeElement.value = finalOptions.dataSourceType;
            }

            // 应用具体配置
            switch (finalOptions.dataSourceType) {
                case 'dataItem':
                    if (finalOptions.deviceId) {
                        const deviceSelect = this.getComponentSpecificElement(widget, 'deviceSelect');
                        if (deviceSelect) deviceSelect.value = finalOptions.deviceId;
                    }
                    if (finalOptions.dataItemId) {
                        const dataItemSelect = this.getComponentSpecificElement(widget, 'dataItemSelect');
                        if (dataItemSelect) dataItemSelect.value = finalOptions.dataItemId;
                    }
                    break;
                case 'static':
                    if (finalOptions.staticLabels) {
                        const staticLabelsElement = this.getComponentSpecificElement(widget, 'staticLabels');
                        if (staticLabelsElement) staticLabelsElement.value = finalOptions.staticLabels;
                    }
                    if (finalOptions.staticValues) {
                        const staticValuesElement = this.getComponentSpecificElement(widget, 'staticValues');
                        if (staticValuesElement) staticValuesElement.value = finalOptions.staticValues;
                    }
                    break;
                case 'externalData':
                    if (finalOptions.dataSetId) {
                        const dataSetSelect = this.getComponentSpecificElement(widget, 'externalDataSetSelect');
                        if (dataSetSelect) dataSetSelect.value = finalOptions.dataSetId;
                    }
                    break;
            }

            // 自动应用配置
            if (finalOptions.autoApply) {
                const config = this.collectDataSourceConfig(widget);

                // 验证配置
                if (finalOptions.validateConfig) {
                    const validation = this.validateDataSourceConfig(widget, config);
                    if (!validation.isValid && finalOptions.repairConfig) {
                        const repairedConfig = this.repairDataSourceConfig(widget, config);
                        console.log(`组件 ${widget.id} 配置已自动修复:`, repairedConfig);
                    }
                }

                console.log(`组件 ${widget.id} 数据源配置完成:`, config);
                return config;
            }

            return true;

        } catch (error) {
            console.error(`配置组件 ${widget.id} 数据源时发生错误:`, error);
            return false;
        }
    }

    /**
     * 快速数据获取
     * 简化的数据获取接口，包含错误处理和缓存
     */
    async quickFetchData(widget, options = {}) {
        if (!widget) {
            console.error('快速获取数据失败：组件对象无效');
            return { success: false, error: '组件对象无效' };
        }

        const defaultOptions = {
            useCache: false,
            timeout: 10000,
            retryCount: 1
        };

        const finalOptions = { ...defaultOptions, ...options };

        try {
            // 检查缓存
            if (finalOptions.useCache) {
                const cacheKey = `${widget.id}_data`;
                const cachedData = this.validationCache.get(cacheKey);
                if (cachedData && (Date.now() - cachedData.timestamp) < 30000) { // 30秒缓存
                    console.log(`使用缓存数据 for 组件 ${widget.id}`);
                    return cachedData.data;
                }
            }

            // 获取数据
            const data = await this.fetchWidgetData(widget);

            // 缓存数据
            if (finalOptions.useCache && data.success) {
                const cacheKey = `${widget.id}_data`;
                this.validationCache.set(cacheKey, {
                    data: data,
                    timestamp: Date.now()
                });
            }

            return data;

        } catch (error) {
            console.error(`快速获取组件 ${widget.id} 数据时发生错误:`, error);

            // 重试机制
            if (finalOptions.retryCount > 0) {
                console.log(`重试获取组件 ${widget.id} 数据，剩余重试次数: ${finalOptions.retryCount}`);
                return this.quickFetchData(widget, {
                    ...finalOptions,
                    retryCount: finalOptions.retryCount - 1
                });
            }

            return { success: false, error: error.message };
        }
    }

    /**
     * 批量组件配置
     * 为多个组件应用相同的数据源配置
     */
    batchConfigureComponents(widgets, commonConfig = {}) {
        if (!Array.isArray(widgets) || widgets.length === 0) {
            console.error('批量配置失败：组件数组无效');
            return { success: false, configured: 0, errors: [] };
        }

        const results = {
            success: true,
            configured: 0,
            errors: [],
            details: []
        };

        for (const widget of widgets) {
            try {
                const result = this.configureComponentDataSource(widget, commonConfig);
                if (result) {
                    results.configured++;
                    results.details.push({
                        widgetId: widget.id,
                        status: 'success',
                        config: result
                    });
                } else {
                    results.errors.push(`组件 ${widget.id} 配置失败`);
                    results.details.push({
                        widgetId: widget.id,
                        status: 'failed',
                        error: '配置失败'
                    });
                }
            } catch (error) {
                results.errors.push(`组件 ${widget.id} 配置异常: ${error.message}`);
                results.details.push({
                    widgetId: widget.id,
                    status: 'error',
                    error: error.message
                });
            }
        }

        if (results.errors.length > 0) {
            results.success = false;
        }

        console.log(`批量配置完成: ${results.configured}/${widgets.length} 个组件配置成功`);
        return results;
    }

    /**
     * 统一的数据获取接口
     */
    async fetchWidgetData(widget) {
        console.log('=== BiDataSourceManager - 开始获取组件数据 ===');
        console.log('组件信息:', {
            id: widget.id,
            type: widget.widgetType || widget.type,
            dataSourceConfig: widget.dataSourceConfig
        });

        // 检查是否为不需要数据源的组件类型
        const componentType = widget.widgetType || widget.type;
        const noDataSourceComponents = ['image-widget', 'decoration-widget', 'html-widget'];

        if (noDataSourceComponents.includes(componentType)) {
            console.log(`组件 ${componentType} 不需要数据源，直接返回成功状态`);
            return {
                success: true,
                message: `${componentType} 组件无需数据源`,
                noDataRequired: true
            };
        }

        let dataSourceConfig = {};

        // 状态指示器组件特殊处理
        if (componentType === 'status-indicator') {
            console.log('处理状态指示器组件数据...');
            // 先解析数据源配置
            try {
                if (widget.dataSourceConfig) {
                    if (typeof widget.dataSourceConfig === 'string') {
                        dataSourceConfig = JSON.parse(widget.dataSourceConfig);
                    } else if (typeof widget.dataSourceConfig === 'object') {
                        dataSourceConfig = widget.dataSourceConfig;
                    }
                }
            } catch (error) {
                console.error('解析状态指示器数据源配置失败:', error);
                dataSourceConfig = {};
            }
            return await this.processStatusIndicatorData(widget, dataSourceConfig);
        }

        // 安全解析数据源配置
        try {
            if (widget.dataSourceConfig) {
                if (typeof widget.dataSourceConfig === 'string') {
                    dataSourceConfig = JSON.parse(widget.dataSourceConfig);
                    console.log('解析字符串格式数据源配置:', dataSourceConfig);
                } else if (typeof widget.dataSourceConfig === 'object') {
                    dataSourceConfig = widget.dataSourceConfig;
                    console.log('使用对象格式数据源配置:', dataSourceConfig);
                }
            } else {
                console.log('没有数据源配置，使用默认配置');
            }
        } catch (parseError) {
            console.error('解析数据源配置失败:', parseError);
            console.log('原始配置:', widget.dataSourceConfig);
            dataSourceConfig = {};
        }

        const dataSourceType = dataSourceConfig.dataSourceType || 'dataItem';
        console.log('确定的数据源类型:', dataSourceType);
        console.log('完整数据源配置:', dataSourceConfig);

        try {
            let result;

            switch (dataSourceType) {
                case 'static':
                    console.log('处理静态数据源...');
                    result = this.processStaticData(widget, dataSourceConfig);
                    break;
                case 'multiData':
                    console.log('处理多数据源...');
                    result = await this.processMultiData(widget, dataSourceConfig);
                    break;
                case 'externalData':
                    console.log('处理外部数据源...');
                    result = await this.processExternalData(widget, dataSourceConfig);
                    break;
                case 'dataItem':
                default:
                    console.log('处理监控项数据源...');
                    result = await this.processDataItemData(widget, dataSourceConfig);
                    break;
            }

            console.log('数据获取结果:', result);
            console.log('=== BiDataSourceManager - 数据获取完成 ===');
            return result;

        } catch (error) {
            console.error('=== BiDataSourceManager - 数据获取失败 ===');
            console.error('错误信息:', error);
            console.error('错误堆栈:', error.stack);
            return { success: false, error: error.message };
        }
    }

    /**
     * 处理静态数据
     */
    processStaticData(widget, dataSourceConfig) {
        console.log('处理静态数据:', dataSourceConfig);
        const staticData = this.parseStaticData(dataSourceConfig);

        if (!staticData) {
            console.warn('静态数据解析失败');
            return { success: false, error: '静态数据解析失败' };
        }

        console.log('解析后的静态数据:', staticData);

        // 根据组件类型进行特殊处理（兼容widget.type和widget.widgetType）
        const componentType = widget.widgetType || widget.type;
        const componentConfig = this.componentConfigs[componentType];
        console.log('静态数据处理 - 组件类型判断:', {
            widgetType: widget.widgetType,
            type: widget.type,
            finalType: componentType,
            hasConfig: !!componentConfig,
            specialHandling: componentConfig ? componentConfig.specialHandling : 'none'
        });

        if (componentConfig && componentConfig.specialHandling === 'singleValue') {
            // 水波图等单值组件：取第一个值
            const result = {
                success: true,
                value: staticData.values && staticData.values.length > 0 ? staticData.values[0] : 0,
                name: '静态数据'
            };
            console.log('单值组件静态数据结果:', result);
            return result;
        } else if (componentType === 'multi-line-chart') {
            // 多折线图：转换为多折线格式
            const result = {
                success: true,
                xAxis: staticData.labels,
                series: [{
                    name: '静态数据',
                    type: 'line',
                    data: staticData.values,
                    yAxisIndex: 0
                }],
                labels: staticData.labels,
                values: staticData.values
            };
            console.log('多折线图静态数据结果:', result);
            return result;
        } else {
            // 其他图表组件：使用完整数据
            console.log('图表组件静态数据结果:', staticData);
            return staticData;
        }
    }

    /**
     * 处理多数据源
     */
    async processMultiData(widget, dataSourceConfig) {
        console.log('处理多数据源:', dataSourceConfig);

        if (!dataSourceConfig.multiDataSources || dataSourceConfig.multiDataSources.length === 0) {
            console.warn('多数据源配置为空');
            return { success: false, error: '多数据源配置为空' };
        }

        console.log('多数据源配置详情:', dataSourceConfig.multiDataSources);

        // 并行获取所有数据源的数据
        const dataPromises = dataSourceConfig.multiDataSources.map(source => {
            console.log(`获取数据源 ${source.label} 的数据，监控项ID: ${source.dataItemId}`);

            if (!source.dataItemId) {
                return Promise.resolve({ label: source.label, value: 0, error: '未配置监控项' });
            }

            return fetch(`/api/bi/data/realtime/${source.dataItemId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log(`数据源 ${source.label} 响应:`, data);
                    if (data.success) {
                        const value = parseFloat(data.value);
                        if (isNaN(value)) {
                            console.warn(`数据源 ${source.label} 数值无效:`, data.value);
                            return { label: source.label, value: 0, error: '数值格式无效' };
                        }
                        return { label: source.label, value: value };
                    } else {
                        console.error(`数据源 ${source.label} 获取失败:`, data);
                        return { label: source.label, value: 0, error: data.error || '获取数据失败' };
                    }
                })
                .catch(error => {
                    console.error(`数据源 ${source.label} 网络错误:`, error);
                    return { label: source.label, value: 0, error: '网络错误' };
                });
        });

        const results = await Promise.all(dataPromises);
        console.log('多数据源获取结果:', results);

        // 根据组件类型构造不同的数据格式（兼容widget.type和widget.widgetType）
        const componentType = widget.widgetType || widget.type;
        console.log('多数据源组件类型:', componentType, '(widgetType:', widget.widgetType, ', type:', widget.type, ')');
        const formattedResult = this.formatMultiDataResults(componentType, results);
        console.log('多数据源格式化结果:', formattedResult);

        return formattedResult;
    }

    /**
     * 处理监控项数据
     */
    async processDataItemData(widget, dataSourceConfig) {
        console.log('=== 处理监控项数据 ===');
        console.log('数据源配置:', dataSourceConfig);
        console.log('组件类型:', widget.widgetType || widget.type);

        const dataItemId = dataSourceConfig.dataItemId;
        console.log('数据项ID:', dataItemId);

        if (!dataItemId) {
            console.log('没有配置数据项ID，返回示例数据');
            const exampleData = this.getExampleData(widget.widgetType || widget.type);
            console.log('返回的示例数据:', exampleData);
            return exampleData;
        }

        const requestData = {
            widgetType: widget.widgetType || widget.type,
            ...dataSourceConfig
        };

        console.log('发送监控项数据请求:', requestData);

        const response = await fetch('/api/bi/data/widget', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(requestData)
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('收到监控项数据响应:', data);

        if (!data.success) {
            throw new Error(data.error || data.message || '服务器返回错误');
        }

        // 验证数据格式并添加调试信息
        const componentType = widget.widgetType || widget.type;
        console.log('监控项数据格式验证:', {
            hasLabels: !!data.labels,
            hasValues: !!data.values,
            hasValue: data.value !== undefined,
            labelsLength: data.labels ? data.labels.length : 0,
            valuesLength: data.values ? data.values.length : 0,
            componentType: componentType,
            allFields: Object.keys(data)
        });

        // 为文本组件进行特殊处理
        if (componentType === 'text-label') {
            console.log('为文本组件处理监控项数据');

            // 如果没有标准的value字段，尝试从其他字段获取数据
            if (data.success && data.value === undefined) {
                if (data.values && data.values.length > 0) {
                    // 使用values数组的第一个值
                    data.value = data.values[0];
                    console.log('文本组件使用values[0]作为value:', data.value);
                } else if (data.labels && data.labels.length > 0) {
                    // 使用labels数组的第一个值
                    data.value = data.labels[0];
                    console.log('文本组件使用labels[0]作为value:', data.value);
                } else {
                    // 检查是否有其他数值字段
                    const numericFields = Object.keys(data).filter(key =>
                        key !== 'success' && key !== 'timestamp' &&
                        typeof data[key] === 'number'
                    );

                    if (numericFields.length > 0) {
                        data.value = data[numericFields[0]];
                        console.log('文本组件使用数值字段', numericFields[0] + ':', data.value);
                    } else {
                        // 使用任何非系统字段的值
                        const availableFields = Object.keys(data).filter(key =>
                            key !== 'success' && key !== 'timestamp' && data[key] !== undefined
                        );

                        if (availableFields.length > 0) {
                            data.value = data[availableFields[0]];
                            console.log('文本组件使用可用字段', availableFields[0] + ':', data.value);
                        }
                    }
                }
            }
        }

        // 为文本组件进行特殊处理
        if (widget.type === 'text-label') {
            console.log('为文本组件处理监控项数据');

            // 如果没有标准的value字段，尝试从其他字段获取数据
            if (data.success && data.value === undefined) {
                if (data.values && data.values.length > 0) {
                    // 使用values数组的第一个值
                    data.value = data.values[0];
                    console.log('文本组件使用values[0]作为value:', data.value);
                } else if (data.labels && data.labels.length > 0) {
                    // 使用labels数组的第一个值
                    data.value = data.labels[0];
                    console.log('文本组件使用labels[0]作为value:', data.value);
                } else {
                    // 检查是否有其他数值字段
                    const numericFields = Object.keys(data).filter(key =>
                        key !== 'success' && key !== 'timestamp' &&
                        typeof data[key] === 'number'
                    );

                    if (numericFields.length > 0) {
                        data.value = data[numericFields[0]];
                        console.log('文本组件使用数值字段', numericFields[0] + ':', data.value);
                    } else {
                        // 使用任何非系统字段的值
                        const availableFields = Object.keys(data).filter(key =>
                            key !== 'success' && key !== 'timestamp' && data[key] !== undefined
                        );

                        if (availableFields.length > 0) {
                            data.value = data[availableFields[0]];
                            console.log('文本组件使用可用字段', availableFields[0] + ':', data.value);
                        }
                    }
                }
            }
        }

        return data;
    }

    /**
     * 格式化多数据源结果
     */
    formatMultiDataResults(componentType, results) {
        if (componentType === 'pie-chart') {
            // 饼图数据格式
            return {
                success: true,
                labels: results.map(item => item.label),
                values: results.map(item => item.value),
                data: results.map(item => ({
                    name: item.label,
                    value: item.value
                }))
            };
        } else if (componentType === 'data-table') {
            // 表格数据格式：data数组，每个元素包含表格行数据（只显示名称和数值）
            const data = results.map(result => ({
                name: result.label,
                value: result.value
            }));

            console.log('表格多数据源格式化结果:', data);
            return {
                success: true,
                data: data
            };
        } else if (componentType === 'multi-line-chart') {
            // 多折线图数据格式：将多个监控项转换为多条折线
            const series = results.map(item => ({
                name: item.label,
                type: 'line',
                data: [item.value], // 单个数值转换为数组
                yAxisIndex: 0
            }));

            console.log('多折线图多数据源格式化结果:', series);
            return {
                success: true,
                xAxis: ['当前值'], // 单个时间点
                series: series
            };
        } else {
            // 柱状图等标准图表数据格式
            return {
                success: true,
                labels: results.map(item => item.label),
                values: results.map(item => item.value)
            };
        }
    }

    /**
     * 解析静态数据
     */
    parseStaticData(dataSourceConfig) {
        const labelsText = dataSourceConfig.staticLabels || '';
        const valuesText = dataSourceConfig.staticValues || '';

        // 解析标签（按行分割，去除空行）
        const labels = labelsText.split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0);

        // 解析数值（按行分割，转换为数字，去除无效值）
        const values = valuesText.split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0)
            .map(line => parseFloat(line))
            .filter(value => !isNaN(value));

        console.log('解析静态数据:', { labels, values });

        // 检查数据有效性
        if (labels.length === 0 || values.length === 0) {
            console.warn('静态数据为空');
            return null;
        }

        // 确保标签和数值数量一致（取较小的长度）
        const dataLength = Math.min(labels.length, values.length);
        const finalLabels = labels.slice(0, dataLength);
        const finalValues = values.slice(0, dataLength);

        // 为饼图创建特殊的数据格式
        const pieData = [];
        for (let i = 0; i < dataLength; i++) {
            pieData.push({
                name: finalLabels[i],
                value: finalValues[i]
            });
        }

        return {
            success: true,
            labels: finalLabels,
            values: finalValues,
            data: pieData
        };
    }

    /**
     * 获取示例数据
     */
    getExampleData(componentType) {
        const examples = {
            'line-chart': {
                success: true,
                labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
                values: [10, 25, 35, 45, 30, 20],
                dataItemName: '示例折线图'
            },
            'bar-chart': {
                success: true,
                labels: ['设备A', '设备B', '设备C', '设备D'],
                values: [65, 59, 80, 81],
                dataItemName: '示例柱状图'
            },
            'horizontal-bar-chart': {
                success: true,
                labels: ['产线A', '产线B', '产线C', '产线D'],
                values: [75, 68, 92, 87],
                dataItemName: '示例水平柱状图'
            },
            'gauge-chart': {
                success: true,
                value: 75,
                unit: '%',
                name: '示例仪表盘'
            },
            'water-chart': {
                success: true,
                value: 65,
                targetValue: 100,
                name: '示例水波图'
            },
            'pie-chart': {
                success: true,
                labels: ['产品A', '产品B', '产品C', '产品D'],
                values: [30, 25, 20, 25],
                data: [
                    { name: '产品A', value: 30 },
                    { name: '产品B', value: 25 },
                    { name: '产品C', value: 20 },
                    { name: '产品D', value: 25 }
                ]
            },
            'multi-line-chart': {
                success: true,
                xAxis: ['07-25', '07-26', '07-27', '07-28', '07-29'],
                series: [
                    {
                        name: '成功',
                        type: 'line',
                        data: [2, 5, 15, 10, 9],
                        yAxisIndex: 0
                    },
                    {
                        name: '失败',
                        type: 'line',
                        data: [10, 20, 30, 12, 16],
                        yAxisIndex: 0
                    }
                ],
                labels: ['07-25', '07-26', '07-27', '07-28', '07-29'],
                values: [2, 5, 15, 10, 9]
            },
            'data-table': {
                success: true,
                data: [
                    { name: '温度传感器', value: 25.6 },
                    { name: '湿度传感器', value: 68.2 },
                    { name: '压力传感器', value: 1.2 }
                ]
            }
        };

        return examples[componentType] || examples['line-chart'];
    }

    /**
     * 收集外部数据源配置（增强版，添加配置验证）
     */
    collectExternalDataConfig(config, widget) {
        // 检查是否启用多数据集模式
        const multiDataSetEnabled = document.getElementById('multiExternalDataSetEnabled')?.checked || false;

        console.log(`收集组件 ${widget?.id} (${widget?.type}) 外部数据源配置，多数据集模式: ${multiDataSetEnabled}`);

        if (multiDataSetEnabled) {
            // 多数据集模式
            const multiConfig = this.collectMultiExternalDataConfig(config, widget);
            console.log(`组件 ${widget?.id} 收集到多数据集配置，数据集数量: ${multiConfig.dataSets?.length || 0}`);
            return multiConfig;
        } else {
            // 单数据集模式
            const singleConfig = this.collectSingleExternalDataConfig(config, widget);
            console.log(`组件 ${widget?.id} 收集到单数据集配置，数据集ID: ${singleConfig.dataSetId}`);
            return singleConfig;
        }
    }

    /**
     * 收集单数据集外部数据源配置（新增，从原有逻辑分离）
     */
    collectSingleExternalDataConfig(config, widget) {
        const dataSetId = document.getElementById('dataSetSelect').value;
        const dataSetName = document.getElementById('dataSetSelect').selectedOptions[0]?.text || '';
        const refreshInterval = parseInt(document.getElementById('refreshInterval').value) || 5;

        config.dataSetId = dataSetId;
        config.dataSetName = dataSetName;
        config.refreshInterval = refreshInterval;

        // 根据组件类型收集不同的字段配置
        if (widget && widget.type === 'data-table') {
            // 表格组件：收集表格字段配置
            config.tableFields = this.collectTableFieldConfig();
        } else {
            // 图表组件：收集标签和数值字段
            const labelField = document.getElementById('labelFieldSelect')?.value || '';
            const valueField = document.getElementById('valueFieldSelect')?.value || '';
            config.labelField = labelField;
            config.valueField = valueField;
        }

        return config;
    }

    /**
     * 收集多外部数据源配置
     */
    collectMultiExternalDataConfig(config, widget) {
        const multiExternalDataConfig = this.getMultiExternalDataSourceConfig(widget.type);
        if (!multiExternalDataConfig) {
            console.warn('组件不支持多外部数据源:', widget.type);
            return config;
        }

        const containerId = multiExternalDataConfig.containerId;
        const container = document.getElementById(containerId);
        if (!container) {
            console.warn('找不到多外部数据源容器:', containerId);
            return config;
        }

        const dataSets = [];
        const dataSetItems = container.querySelectorAll('.multi-external-dataset-item');

        console.log('收集多数据集配置 - 容器ID:', containerId);
        console.log('收集多数据集配置 - 找到数据集项数量:', dataSetItems.length);

        // 按照DOM顺序收集配置，确保顺序一致性
        Array.from(dataSetItems).forEach((item, domIndex) => {
            const dataSetSelect = item.querySelector('.dataset-select');
            const labelFieldSelect = item.querySelector('.label-field-select');
            const valueFieldSelect = item.querySelector('.value-field-select');
            const aliasInput = item.querySelector('.dataset-alias-input');

            console.log(`数据集项 DOM索引${domIndex}:`, {
                itemId: item.id,
                dataSetId: dataSetSelect ? dataSetSelect.value : 'null',
                dataSetName: dataSetSelect && dataSetSelect.selectedOptions[0] ? dataSetSelect.selectedOptions[0].text : 'null',
                labelField: labelFieldSelect ? labelFieldSelect.value : 'null',
                valueField: valueFieldSelect ? valueFieldSelect.value : 'null',
                alias: aliasInput ? aliasInput.value : 'null'
            });

            if (dataSetSelect && dataSetSelect.value) {
                // 使用DOM索引而不是ID中的索引，确保配置顺序与DOM顺序一致
                const dataSetConfig = {
                    dataSetId: dataSetSelect.value,
                    dataSetName: dataSetSelect.selectedOptions[0]?.text || '',
                    alias: aliasInput ? aliasInput.value : `数据集${domIndex + 1}`,
                    domIndex: domIndex  // 添加DOM索引用于调试
                };

                // 根据组件类型收集字段配置
                if (widget && widget.type === 'data-table') {
                    // 表格组件：收集表格字段配置
                    const tableFieldsContainer = item.querySelector('.table-fields-container');
                    if (tableFieldsContainer) {
                        dataSetConfig.tableFields = this.collectTableFieldConfigFromContainer(tableFieldsContainer);
                    }
                } else {
                    // 图表组件：收集标签和数值字段
                    const labelFieldValue = labelFieldSelect ? labelFieldSelect.value : '';
                    const valueFieldValue = valueFieldSelect ? valueFieldSelect.value : '';

                    dataSetConfig.labelField = labelFieldValue;
                    dataSetConfig.valueField = valueFieldValue;

                    console.log(`图表组件字段配置 DOM索引${domIndex}:`, {
                        labelFieldSelect: labelFieldSelect ? 'found' : 'not found',
                        valueFieldSelect: valueFieldSelect ? 'found' : 'not found',
                        labelFieldValue: labelFieldValue,
                        valueFieldValue: valueFieldValue
                    });
                }

                console.log(`添加数据集配置 DOM索引${domIndex}:`, dataSetConfig);
                dataSets.push(dataSetConfig);
            } else {
                console.log(`跳过数据集项 DOM索引${domIndex}: 未选择数据集`);
            }
        });

        console.log('最终收集到的数据集配置:', dataSets);

        config.multiDataSet = true;
        config.dataSets = dataSets;
        config.refreshInterval = parseInt(document.getElementById('refreshInterval').value) || 5;

        // 数据合并策略
        const mergeStrategy = document.getElementById('dataMergeStrategy')?.value || 'union';
        config.mergeStrategy = mergeStrategy;

        return config;
    }

    /**
     * 收集表格字段配置
     */
    collectTableFieldConfig() {
        const tableFieldList = document.getElementById('tableFieldList');
        if (!tableFieldList) {
            console.log('collectTableFieldConfig: 找不到 tableFieldList 元素');
            return [];
        }

        const fields = [];
        console.log('collectTableFieldConfig: 开始收集字段配置');

        // 遍历所有字段卡片元素
        const fieldCards = tableFieldList.querySelectorAll('.card[id^="tableField_"]');
        console.log('collectTableFieldConfig: 找到字段卡片数量:', fieldCards.length);

        fieldCards.forEach((card, index) => {
            // 从卡片中查找输入元素
            const nameInput = card.querySelector('input[id^="tableFieldName_"]');
            const mappingSelect = card.querySelector('select[id^="tableFieldMapping_"]');

            console.log(`collectTableFieldConfig: 字段 ${index + 1}:`, {
                nameInput: nameInput ? nameInput.id : 'null',
                mappingSelect: mappingSelect ? mappingSelect.id : 'null',
                nameValue: nameInput ? nameInput.value : '',
                mappingValue: mappingSelect ? mappingSelect.value : ''
            });

            if (nameInput && mappingSelect && nameInput.value && mappingSelect.value) {
                const fieldConfig = {
                    displayName: nameInput.value,
                    dataField: mappingSelect.value
                };
                fields.push(fieldConfig);
                console.log('collectTableFieldConfig: 添加字段配置:', fieldConfig);
            } else {
                console.log(`collectTableFieldConfig: 字段 ${index + 1} 配置不完整，跳过`);
            }
        });

        console.log('collectTableFieldConfig: 最终收集到的字段配置:', fields);
        return fields;
    }

    /**
     * 从指定容器收集表格字段配置
     */
    collectTableFieldConfigFromContainer(container) {
        if (!container) {
            console.log('collectTableFieldConfigFromContainer: 容器为空');
            return [];
        }

        const fields = [];
        const fieldCards = container.querySelectorAll('.card[id*="tableField_"]');

        fieldCards.forEach((card, index) => {
            const nameInput = card.querySelector('input[id*="tableFieldName_"]');
            const mappingSelect = card.querySelector('select[id*="tableFieldMapping_"]');

            if (nameInput && mappingSelect && nameInput.value && mappingSelect.value) {
                const fieldConfig = {
                    displayName: nameInput.value,
                    dataField: mappingSelect.value
                };
                fields.push(fieldConfig);
            }
        });

        return fields;
    }

    /**
     * 恢复外部数据源配置（增强版，防止组件间配置污染）
     */
    restoreExternalDataConfig(widget, dataSourceConfig) {
        console.log('恢复外部数据源配置，组件信息:', {
            id: widget?.id,
            type: widget?.type,
            widgetType: widget?.widgetType,
            isMultiDataSet: dataSourceConfig.multiDataSet
        });

        // 验证配置的有效性
        if (!widget || !dataSourceConfig) {
            console.warn('恢复外部数据源配置失败：缺少必要参数');
            return;
        }

        // 检查是否为多数据集模式
        if (dataSourceConfig.multiDataSet && dataSourceConfig.dataSets) {
            console.log(`组件 ${widget.id} 恢复多数据集配置，数据集数量: ${dataSourceConfig.dataSets.length}`);
            this.restoreMultiExternalDataConfig(widget, dataSourceConfig);
            return;
        }

        // 单数据集模式恢复
        console.log(`组件 ${widget.id} 恢复单数据集配置，数据集ID: ${dataSourceConfig.dataSetId}`);
        this.restoreSingleExternalDataConfig(widget, dataSourceConfig);
    }

    /**
     * 恢复单数据集外部数据源配置（新增，从原有逻辑分离）
     */
    restoreSingleExternalDataConfig(widget, dataSourceConfig) {
        const dataSetSelect = document.getElementById('dataSetSelect');
        const labelFieldSelect = document.getElementById('labelFieldSelect');
        const valueFieldSelect = document.getElementById('valueFieldSelect');

        // 确保多数据集开关处于禁用状态
        const multiEnabled = document.getElementById('multiExternalDataSetEnabled');
        if (multiEnabled) {
            multiEnabled.checked = false;
            console.log(`组件 ${widget.id} 确保多数据集开关禁用`);
        }

        if (dataSetSelect && dataSourceConfig.dataSetId) {
            dataSetSelect.value = dataSourceConfig.dataSetId;
            console.log(`组件 ${widget.id} 恢复数据集选择: ${dataSourceConfig.dataSetId}`);

            // 触发数据集选择事件以加载字段
            if (typeof onDataSetChange === 'function') {
                onDataSetChange();
            }

            // 延迟恢复字段选择，等待字段加载完成
            setTimeout(() => {
                if (labelFieldSelect && dataSourceConfig.labelField) {
                    labelFieldSelect.value = dataSourceConfig.labelField;
                    console.log(`组件 ${widget.id} 恢复标签字段: ${dataSourceConfig.labelField}`);
                }
                if (valueFieldSelect && dataSourceConfig.valueField) {
                    valueFieldSelect.value = dataSourceConfig.valueField;
                    console.log(`组件 ${widget.id} 恢复数值字段: ${dataSourceConfig.valueField}`);
                }
            }, 1000);
        }

        // 恢复刷新间隔
        if (dataSourceConfig.refreshInterval) {
            const refreshIntervalInput = document.getElementById('refreshInterval');
            if (refreshIntervalInput) {
                refreshIntervalInput.value = dataSourceConfig.refreshInterval;
                console.log(`组件 ${widget.id} 恢复刷新间隔: ${dataSourceConfig.refreshInterval}`);
            }
        }
    }

    /**
     * 恢复多外部数据源配置（重构版，参考单数据集模式）
     */
    restoreMultiExternalDataConfig(widget, dataSourceConfig) {
        console.log('开始恢复多外部数据源配置（重构版）:', {
            widget: {
                id: widget?.id,
                type: widget?.type,
                widgetType: widget?.widgetType
            },
            dataSourceConfig: dataSourceConfig
        });

        // 检查是否已经在恢复过程中
        if (window.isRestoringMultiDataSet) {
            console.log('多数据集配置恢复已在进行中，跳过重复执行');
            return;
        }

        // 启用多数据集模式
        const multiEnabledCheckbox = document.getElementById('multiExternalDataSetEnabled');
        if (multiEnabledCheckbox) {
            multiEnabledCheckbox.checked = true;
            console.log(`组件 ${widget.id} 启用多数据集模式`);

            // 触发多数据集模式切换
            if (typeof onMultiExternalDataSetToggle === 'function') {
                onMultiExternalDataSetToggle();
            }
        } else {
            console.error(`组件 ${widget.id} 多数据集开关元素未找到`);
        }

        // 恢复合并策略
        const mergeStrategySelect = document.getElementById('dataMergeStrategy');
        if (mergeStrategySelect && dataSourceConfig.mergeStrategy) {
            mergeStrategySelect.value = dataSourceConfig.mergeStrategy;
        }

        // 使用简单的回调机制等待界面初始化完成（参考单数据集模式）
        this.waitForMultiDataSetInterfaceReady(widget, () => {
            console.log('多数据集界面初始化完成，开始恢复数据集配置');
            this.restoreDataSetsConfigWithCallback(widget, dataSourceConfig.dataSets);
        });
    }

    /**
     * 等待多数据集界面就绪（简化版，参考单数据集模式）
     */
    waitForMultiDataSetInterfaceReady(widget, callback) {
        // 从传入的widget参数获取组件类型，而不是依赖全局状态
        const componentType = widget?.type || widget?.widgetType;

        console.log('等待多数据集界面就绪，组件类型:', componentType);

        const multiExternalDataConfig = this.getMultiExternalDataSourceConfig(componentType);

        if (!multiExternalDataConfig) {
            console.error('组件不支持多外部数据源:', componentType);
            console.error('可用的组件配置:', Object.keys(this.componentConfigs));
            return;
        }

        const container = document.getElementById(multiExternalDataConfig.containerId);
        if (!container) {
            console.error('找不到多数据集容器:', multiExternalDataConfig.containerId);
            return;
        }

        // 简单检查界面是否就绪
        if (container.style.display !== 'none') {
            console.log('多数据集界面已就绪，容器ID:', multiExternalDataConfig.containerId);
            callback();
        } else {
            // 等待界面显示
            setTimeout(() => {
                this.waitForMultiDataSetInterfaceReady(widget, callback);
            }, 100);
        }
    }

    /**
     * 恢复数据集配置列表（重构版，使用回调机制）
     */
    restoreDataSetsConfigWithCallback(widget, dataSets) {
        if (!dataSets || dataSets.length === 0) {
            console.log('没有数据集配置需要恢复');
            return;
        }

        // 检查是否已经在恢复过程中，避免重复执行
        if (window.isRestoringMultiDataSet) {
            console.log('多数据集配置恢复已在进行中，跳过重复执行');
            return;
        }

        console.log('开始恢复数据集配置列表，数量:', dataSets.length);

        // 从传入的widget参数获取组件类型
        const componentType = widget?.type || widget?.widgetType;
        const multiExternalDataConfig = this.getMultiExternalDataSourceConfig(componentType);

        if (!multiExternalDataConfig) {
            console.error('组件不支持多外部数据源:', componentType);
            console.error('组件信息:', widget);
            return;
        }

        const container = document.getElementById(multiExternalDataConfig.containerId);
        if (!container) {
            console.error('找不到多数据集容器:', multiExternalDataConfig.containerId);
            return;
        }

        // 设置恢复标志，防止重复执行
        window.isRestoringMultiDataSet = true;
        console.log('设置数据集配置恢复保护标志');

        // 清空现有配置
        container.innerHTML = '';

        // 串行恢复每个数据集配置（参考单数据集模式的处理方式）
        this.restoreDataSetSequentially(dataSets, 0, multiExternalDataConfig.containerId);
    }

    /**
     * 串行恢复数据集配置（参考单数据集模式，增强错误处理）
     */
    restoreDataSetSequentially(dataSets, index, containerId) {
        if (index >= dataSets.length) {
            console.log('所有数据集配置恢复完成');

            // 清理重复的DOM元素
            this.cleanupDuplicateDataSetItems();

            // 延迟清除配置恢复标志，确保所有异步操作完成
            setTimeout(() => {
                window.isRestoringMultiDataSet = false;
                console.log('清除多数据集配置恢复保护标志');
            }, 500);

            return;
        }

        const dataSetConfig = dataSets[index];
        console.log(`恢复数据集配置 ${index + 1}/${dataSets.length}:`, dataSetConfig);

        try {
            // 添加数据集配置项
            if (typeof addExternalDataSetToContainer === 'function') {
                addExternalDataSetToContainer(containerId, index);

                // 等待DOM元素创建完成后恢复配置
                setTimeout(() => {
                    this.restoreSingleDataSetConfigWithCallback(index, dataSetConfig, () => {
                        // 当前数据集配置恢复完成（无论成功还是失败），继续下一个
                        console.log(`数据集 ${index + 1} 处理完成，继续处理下一个`);
                        this.restoreDataSetSequentially(dataSets, index + 1, containerId);
                    });
                }, 200);
            } else {
                console.error('addExternalDataSetToContainer函数不存在');
                // 即使函数不存在也要继续下一个
                this.restoreDataSetSequentially(dataSets, index + 1, containerId);
            }
        } catch (error) {
            console.error(`处理数据集 ${index + 1} 时发生错误:`, error);
            // 发生错误时也要继续处理下一个数据集
            this.restoreDataSetSequentially(dataSets, index + 1, containerId);
        }
    }

    /**
     * 清除恢复标志（错误恢复机制）
     */
    clearRestoringFlag() {
        if (window.isRestoringMultiDataSet) {
            window.isRestoringMultiDataSet = false;
            console.log('强制清除多数据集配置恢复保护标志');
        }
    }

    /**
     * 清理重复的DOM元素（修复ID重复问题）
     */
    cleanupDuplicateDataSetItems() {
        console.log('开始清理重复的数据集DOM元素');

        // 获取所有多数据集容器ID
        const containerIds = [
            'pieExternalDataSourceList',
            'barExternalDataSourceList',
            'horizontalBarExternalDataSourceList',
            'lineExternalDataSourceList',
            'tableExternalDataSourceList'
        ];

        containerIds.forEach(containerId => {
            const container = document.getElementById(containerId);
            if (!container) return;

            // 收集当前容器内所有数据集项的ID
            const dataSetItems = container.querySelectorAll('[id^="externalDataSet_"]');
            const seenIds = new Set();
            const duplicates = [];

            dataSetItems.forEach(item => {
                if (seenIds.has(item.id)) {
                    duplicates.push(item);
                    console.log(`容器 ${containerId} 中发现重复的数据集项ID: ${item.id}`);
                } else {
                    seenIds.add(item.id);
                }
            });

            // 移除重复的元素
            duplicates.forEach(duplicate => {
                console.log(`从容器 ${containerId} 移除重复的数据集项: ${duplicate.id}`);
                duplicate.remove();
            });

            if (duplicates.length > 0) {
                console.log(`容器 ${containerId} 清理完成，移除了 ${duplicates.length} 个重复的数据集项`);
            }
        });
    }

    /**
     * 恢复数据集配置列表（保持向后兼容）
     */
    restoreDataSetsConfig(dataSets) {
        // 尝试从全局状态获取组件信息作为备用
        const widget = window.selectedWidget;
        this.restoreDataSetsConfigWithCallback(widget, dataSets);
    }

    /**
     * 恢复单个数据集配置（重构版，使用回调机制，增强错误处理）
     */
    restoreSingleDataSetConfigWithCallback(index, dataSetConfig, callback) {
        console.log(`开始恢复单个数据集配置，索引: ${index}`, dataSetConfig);

        try {
            const dataSetItem = document.getElementById(`externalDataSet_${index}`);
            if (!dataSetItem) {
                console.error(`找不到数据集项，ID: externalDataSet_${index}`);
                if (callback) callback();
                return;
            }

            // 首先加载数据集列表到该配置项的select元素
            const dataSetSelect = dataSetItem.querySelector('.dataset-select');
            if (dataSetSelect) {
                this.loadDataSetListForDataSetItem(dataSetSelect, () => {
                    try {
                        // 数据集列表加载完成后，恢复数据集选择
                        if (dataSetConfig.dataSetId) {
                            dataSetSelect.value = dataSetConfig.dataSetId;
                            console.log(`设置数据集选择: ${dataSetConfig.dataSetId}`);

                            // 触发数据集选择事件以加载字段
                            try {
                                if (typeof onExternalDataSetChange === 'function') {
                                    onExternalDataSetChange(index);
                                }
                            } catch (changeError) {
                                console.error(`触发数据集选择事件失败，索引: ${index}`, changeError);
                                // 即使事件触发失败，也继续恢复其他配置
                            }

                            // 等待字段加载完成后恢复字段配置
                            this.waitForFieldsLoadedInDataSetItem(dataSetItem, () => {
                                try {
                                    this.restoreFieldsConfigForDataSetItem(dataSetItem, dataSetConfig);
                                } catch (fieldsError) {
                                    console.error(`恢复字段配置失败，索引: ${index}`, fieldsError);
                                    // 字段配置失败不影响别名恢复
                                }

                                // 恢复别名
                                try {
                                    const aliasInput = dataSetItem.querySelector('.dataset-alias-input');
                                    if (aliasInput && dataSetConfig.alias) {
                                        aliasInput.value = dataSetConfig.alias;
                                        console.log(`设置数据集别名: ${dataSetConfig.alias}`);
                                    }
                                } catch (aliasError) {
                                    console.error(`恢复别名失败，索引: ${index}`, aliasError);
                                }

                                console.log(`单个数据集配置恢复完成，索引: ${index}`);
                                if (callback) callback();
                            });
                        } else {
                            console.log(`数据集配置 ${index} 没有dataSetId，跳过字段恢复`);
                            if (callback) callback();
                        }
                    } catch (restoreError) {
                        console.error(`恢复数据集配置时发生错误，索引: ${index}`, restoreError);
                        if (callback) callback();
                    }
                });
            } else {
                console.error(`找不到数据集选择元素，索引: ${index}`);
                if (callback) callback();
            }
        } catch (error) {
            console.error(`恢复单个数据集配置时发生严重错误，索引: ${index}`, error);
            // 确保即使发生严重错误也要调用回调，避免阻塞后续数据集的恢复
            if (callback) callback();
        }
    }

    /**
     * 恢复单个数据集配置（保持向后兼容）
     */
    restoreSingleDataSetConfig(index, dataSetConfig) {
        this.restoreSingleDataSetConfigWithCallback(index, dataSetConfig, null);
    }

    /**
     * 为数据集项加载数据集列表（参考单数据集模式）
     */
    loadDataSetListForDataSetItem(selectElement, callback) {
        if (!selectElement) {
            if (callback) callback();
            return;
        }

        // 显示加载状态
        selectElement.innerHTML = '<option value="">加载中...</option>';

        fetch('/api/bi/datasets')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let options = '<option value="">请选择数据集</option>';
                    data.data.forEach(dataset => {
                        options += `<option value="${dataset.id}" data-name="${dataset.name}">${dataset.name}</option>`;
                    });
                    selectElement.innerHTML = options;
                    console.log('数据集项的数据集列表加载成功');
                    if (callback) callback();
                } else {
                    selectElement.innerHTML = '<option value="">加载失败</option>';
                    console.error('加载数据集列表失败:', data.message);
                    if (callback) callback();
                }
            })
            .catch(error => {
                selectElement.innerHTML = '<option value="">加载失败</option>';
                console.error('加载数据集列表失败:', error);
                if (callback) callback();
            });
    }

    /**
     * 等待数据集项中的字段加载完成（增强错误处理和超时机制）
     */
    waitForFieldsLoadedInDataSetItem(dataSetItem, callback) {
        const labelFieldSelect = dataSetItem.querySelector('.label-field-select');
        const valueFieldSelect = dataSetItem.querySelector('.value-field-select');

        if (!labelFieldSelect && !valueFieldSelect) {
            // 如果没有字段选择元素（可能是表格组件），直接回调
            console.log('没有字段选择元素，直接继续');
            if (callback) callback();
            return;
        }

        let checkCount = 0;
        const maxChecks = 50; // 最多检查50次，约5秒超时

        // 检查字段是否已加载
        const checkFieldsLoaded = () => {
            checkCount++;

            try {
                const labelLoaded = !labelFieldSelect || (labelFieldSelect.options.length > 1 && !labelFieldSelect.innerHTML.includes('加载中'));
                const valueLoaded = !valueFieldSelect || (valueFieldSelect.options.length > 1 && !valueFieldSelect.innerHTML.includes('加载中'));

                if (labelLoaded && valueLoaded) {
                    console.log('数据集项字段加载完成');
                    if (callback) callback();
                } else if (checkCount >= maxChecks) {
                    console.warn('字段加载超时，强制继续处理');
                    if (callback) callback();
                } else {
                    setTimeout(checkFieldsLoaded, 100);
                }
            } catch (error) {
                console.error('检查字段加载状态时发生错误:', error);
                // 即使出错也要继续，避免阻塞整个恢复过程
                if (callback) callback();
            }
        };

        // 开始检查
        setTimeout(checkFieldsLoaded, 200);
    }

    /**
     * 恢复数据集项的字段配置
     */
    restoreFieldsConfigForDataSetItem(dataSetItem, dataSetConfig) {
        // 恢复图表字段配置
        const labelFieldSelect = dataSetItem.querySelector('.label-field-select');
        const valueFieldSelect = dataSetItem.querySelector('.value-field-select');

        if (labelFieldSelect && dataSetConfig.labelField) {
            labelFieldSelect.value = dataSetConfig.labelField;
            console.log(`设置标签字段: ${dataSetConfig.labelField}`);
        }
        if (valueFieldSelect && dataSetConfig.valueField) {
            valueFieldSelect.value = dataSetConfig.valueField;
            console.log(`设置数值字段: ${dataSetConfig.valueField}`);
        }

        // 恢复表格字段配置
        if (dataSetConfig.tableFields) {
            this.restoreTableFieldsConfig(dataSetItem, dataSetConfig.tableFields);
        }
    }

    /**
     * 恢复表格字段配置
     */
    restoreTableFieldsConfig(dataSetItem, tableFields) {
        const tableFieldsList = dataSetItem.querySelector('.table-fields-list');
        if (!tableFieldsList || !tableFields) return;

        // 清空现有字段配置
        tableFieldsList.innerHTML = '';

        // 恢复每个字段配置
        tableFields.forEach((fieldConfig, index) => {
            if (typeof addTableFieldToDataSetWithField === 'function') {
                addTableFieldToDataSetWithField(
                    dataSetItem,
                    index,
                    fieldConfig.displayName || '',
                    fieldConfig.dataField || ''
                );
            }
        });
    }

    /**
     * 处理外部数据源数据
     */
    async processExternalData(widget, dataSourceConfig) {
        try {
            // 检查是否为多数据集模式
            if (dataSourceConfig.multiDataSet && dataSourceConfig.dataSets) {
                console.log('处理多外部数据源数据:', dataSourceConfig);
                return await this.processMultiExternalData(widget, dataSourceConfig);
            }

            // 单数据集模式（原有逻辑）
            if (!dataSourceConfig.dataSetId) {
                throw new Error('未选择数据集');
            }

            console.log('获取外部数据源数据:', dataSourceConfig);

            // 构建API URL，包含字段选择参数
            let apiUrl = `/api/bi/dataset/${dataSourceConfig.dataSetId}/data`;
            const params = new URLSearchParams();

            // 根据组件类型添加不同的参数
            const componentType = widget.widgetType || widget.type;
            if (componentType === 'data-table' && dataSourceConfig.tableFields) {
                // 表格组件：通过POST请求发送tableFields配置
                console.log('表格组件使用POST请求发送字段配置');
                const requestBody = {
                    tableFields: dataSourceConfig.tableFields
                };

                const response = await fetch(`/api/bi/data/widget`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        widgetType: 'data-table',
                        dataSourceType: 'externalData',
                        dataSetId: dataSourceConfig.dataSetId,
                        tableFields: dataSourceConfig.tableFields
                    })
                });

                if (!response.ok) {
                    throw new Error(`获取表格数据失败: ${response.status}`);
                }

                const result = await response.json();
                console.log('表格外部数据源API响应:', result);
                return result;
            } else {
                // 图表组件：使用GET请求
                if (dataSourceConfig.labelField) {
                    params.append('labelField', dataSourceConfig.labelField);
                }
                if (dataSourceConfig.valueField) {
                    params.append('valueField', dataSourceConfig.valueField);
                }

                if (params.toString()) {
                    apiUrl += '?' + params.toString();
                }

                // 调用数据集API获取数据
                const response = await fetch(apiUrl);

                if (!response.ok) {
                    throw new Error(`获取数据集数据失败: ${response.status}`);
                }

                const result = await response.json();
                console.log('外部数据源API响应:', result);

                if (!result.success) {
                    throw new Error(result.message || '获取数据集数据失败');
                }

                console.log('外部数据源原始数据:', result.data);

                // 为数据添加字段选择信息
                if (result.data && typeof result.data === 'object') {
                    result.data.labelField = dataSourceConfig.labelField;
                    result.data.valueField = dataSourceConfig.valueField;
                }

                // 转换数据格式以适配图表组件，传递完整的数据源配置
                const formattedData = this.formatExternalDataForChart(result.data, componentType, dataSourceConfig);
                console.log('外部数据源格式化结果:', formattedData);

                return formattedData;
            }

        } catch (error) {
            console.error('处理外部数据源失败:', error);
            return this.getErrorData(error.message);
        }
    }

    /**
     * 处理多外部数据源数据
     */
    async processMultiExternalData(widget, dataSourceConfig) {
        try {
            const dataSets = dataSourceConfig.dataSets || [];
            if (dataSets.length === 0) {
                throw new Error('未配置数据集');
            }

            console.log('处理多外部数据源，数据集数量:', dataSets.length);

            const componentType = widget.widgetType || widget.type;
            const mergeStrategy = dataSourceConfig.mergeStrategy || 'union';

            // 并行获取所有数据集的数据
            const dataPromises = dataSets.map(async (dataSetConfig, index) => {
                try {
                    console.log(`获取数据集 ${index + 1} (实际索引: ${index}):`, dataSetConfig);

                    let apiUrl = `/api/bi/dataset/${dataSetConfig.dataSetId}/data`;
                    const params = new URLSearchParams();

                    // 根据组件类型添加参数
                    if (componentType === 'data-table' && dataSetConfig.tableFields) {
                        // 表格组件使用POST请求
                        const response = await fetch(apiUrl, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ tableFields: dataSetConfig.tableFields })
                        });
                        const result = await response.json();
                        return { ...result, alias: dataSetConfig.alias, index };
                    } else {
                        // 图表组件使用GET请求
                        console.log(`数据集 ${index + 1} 字段配置:`, {
                            labelField: dataSetConfig.labelField,
                            valueField: dataSetConfig.valueField
                        });

                        if (dataSetConfig.labelField) params.append('labelField', dataSetConfig.labelField);
                        if (dataSetConfig.valueField) params.append('valueField', dataSetConfig.valueField);

                        if (params.toString()) {
                            apiUrl += '?' + params.toString();
                        }

                        console.log(`数据集 ${index + 1} API请求URL:`, apiUrl);
                        const response = await fetch(apiUrl);
                        const result = await response.json();
                        console.log(`数据集 ${index + 1} API响应:`, result);
                        return { ...result, alias: dataSetConfig.alias, index };
                    }
                } catch (error) {
                    console.error(`获取数据集 ${index + 1} 失败:`, error);
                    return {
                        success: false,
                        error: error.message,
                        alias: dataSetConfig.alias,
                        index
                    };
                }
            });

            // 等待所有数据集查询完成
            const results = await Promise.all(dataPromises);
            console.log('所有数据集查询完成:', results);

            // 合并数据
            return this.mergeMultiExternalData(results, mergeStrategy, componentType);

        } catch (error) {
            console.error('处理多外部数据源失败:', error);
            return this.getErrorData(error.message);
        }
    }

    /**
     * 格式化外部数据为图表格式
     */
    formatExternalDataForChart(data, chartType, dataSourceConfig = {}) {
        try {
            console.log('格式化外部数据源数据:', data, '图表类型:', chartType, '数据源配置:', dataSourceConfig);

            // 检查数据是否为空
            if (!data) {
                console.warn('外部数据源数据为空');
                return this.getErrorData('数据集为空');
            }

            // 检查数据格式：后端返回的是 {labels: [], values: []} 格式
            if (data.labels && data.values && Array.isArray(data.labels) && Array.isArray(data.values)) {
                console.log('使用标准格式数据:', {
                    labelsLength: data.labels.length,
                    valuesLength: data.values.length,
                    labels: data.labels,
                    values: data.values
                });

                // 确保标签和数值数量一致
                const dataLength = Math.min(data.labels.length, data.values.length);
                const finalLabels = data.labels.slice(0, dataLength);
                const finalValues = data.values.slice(0, dataLength);

                // 为仪表盘、水波图和柱状百分比图提供数值格式
                if (chartType === 'gauge-chart' || chartType === 'water-chart' || chartType === 'column-percentage-chart') {
                    const singleValue = this.extractSingleValue(finalLabels, finalValues);
                    const result = {
                        success: true,
                        value: singleValue,
                        unit: '',
                        name: '外部数据'
                    };
                    console.log('单值组件数据格式:', result, '组件类型:', chartType);
                    return result;
                }

                // 为文本组件提供特殊格式，支持字段选择和文本内容
                if (chartType === 'text-label') {
                    const textContent = this.extractTextContent(finalLabels, finalValues, data.labelField, data.valueField);
                    const result = {
                        success: true,
                        textContent: textContent,
                        labelField: data.labelField,
                        valueField: data.valueField,
                        labels: finalLabels,
                        values: finalValues
                    };
                    console.log('文本组件数据格式:', result);
                    return result;
                }

                // 为图表组件提供标准格式
                const result = {
                    success: true,
                    labels: finalLabels,
                    values: finalValues
                };

                // 为饼图添加特殊格式
                if (chartType === 'pie-chart') {
                    result.data = finalLabels.map((label, index) => ({
                        name: label,
                        value: finalValues[index] || 0
                    }));
                    console.log('饼图数据格式:', result.data);
                }

                // 为多折线图添加特殊格式 - 适配外部数据源规范
                if (chartType === 'multi-line-chart') {
                    // 使用数据集别名作为系列名称（优先级：别名 > 数据集名称 > 默认值）
                    const seriesName = dataSourceConfig.alias || dataSourceConfig.dataSetName || data.dataSetName || '数据集';

                    // 检查数据量并进行性能优化
                    const totalDataPoints = finalLabels.length;
                    const maxDataPoints = 1000;

                    let optimizedLabels = finalLabels;
                    let optimizedValues = finalValues;
                    let performanceMessage = '';

                    if (totalDataPoints > maxDataPoints) {
                        console.warn(`单数据集数据量过大 (${totalDataPoints} 个数据点)，启用采样优化`);

                        // 简单均匀采样
                        const interval = Math.floor(totalDataPoints / maxDataPoints);
                        optimizedLabels = [];
                        optimizedValues = [];

                        for (let i = 0; i < totalDataPoints; i += interval) {
                            if (optimizedLabels.length < maxDataPoints) {
                                optimizedLabels.push(finalLabels[i]);
                                optimizedValues.push(finalValues[i]);
                            }
                        }

                        // 确保包含最后一个数据点
                        if (optimizedLabels.length > 0 && optimizedLabels[optimizedLabels.length - 1] !== finalLabels[totalDataPoints - 1]) {
                            optimizedLabels.push(finalLabels[totalDataPoints - 1]);
                            optimizedValues.push(finalValues[totalDataPoints - 1]);
                        }

                        performanceMessage = ` (已优化：${totalDataPoints} → ${optimizedLabels.length} 个数据点)`;
                        console.log(`单数据集采样完成：${totalDataPoints} → ${optimizedLabels.length} 个数据点`);
                    }

                    // 转换为多折线图标准格式，适配外部数据源
                    result.xAxis = optimizedLabels;
                    result.series = [{
                        name: seriesName,           // 使用别名作为系列名称
                        type: 'line',
                        data: optimizedValues,
                        yAxisIndex: 0
                    }];

                    // 设置图例数据，对应折线数量
                    result.legendData = [seriesName];

                    // 性能信息
                    result.originalDataPoints = totalDataPoints;
                    result.optimizedDataPoints = optimizedLabels.length;
                    result.isOptimized = totalDataPoints > maxDataPoints;

                    if (performanceMessage) {
                        result.message = (result.message || '数据处理完成') + performanceMessage;
                    }

                    console.log('多折线图单数据集格式化完成:', {
                        seriesName: seriesName,
                        xAxisLength: result.xAxis.length,
                        seriesCount: result.series.length,
                        legendData: result.legendData,
                        isOptimized: result.isOptimized
                    });
                }

                console.log('格式化后的外部数据:', result);
                return result;
            } else if (Array.isArray(data)) {
                // 如果是数组格式，转换为标准格式
                console.log('转换数组格式数据:', data);
                return this.convertRawDataToChartFormat(data, chartType, dataSourceConfig);
            } else {
                console.error('不支持的数据格式:', data);
                return this.getErrorData('不支持的数据格式');
            }

        } catch (error) {
            console.error('格式化外部数据失败:', error);
            return this.getErrorData('数据格式化失败: ' + error.message);
        }
    }

    /**
     * 从数组数据中提取单个数值（用于仪表盘、水波图、柱状百分比图等单值组件）
     */
    extractSingleValue(labels, values) {
        if (!values || values.length === 0) {
            console.warn('数值数组为空，返回默认值0');
            return 0;
        }

        // 默认使用最后一个值（最新数据）
        const lastValue = values[values.length - 1];

        // 确保返回的是数值类型
        if (typeof lastValue === 'number') {
            console.log('提取单值:', lastValue, '来源: 最后一个值');
            return lastValue;
        } else if (lastValue !== null && lastValue !== undefined) {
            const numValue = parseFloat(lastValue);
            if (!isNaN(numValue)) {
                console.log('提取单值:', numValue, '来源: 转换后的最后一个值');
                return numValue;
            }
        }

        // 如果最后一个值无效，尝试找第一个有效的数值
        for (let i = 0; i < values.length; i++) {
            const value = values[i];
            if (typeof value === 'number') {
                console.log('提取单值:', value, '来源: 第', i + 1, '个有效值');
                return value;
            } else if (value !== null && value !== undefined) {
                const numValue = parseFloat(value);
                if (!isNaN(numValue)) {
                    console.log('提取单值:', numValue, '来源: 转换后的第', i + 1, '个值');
                    return numValue;
                }
            }
        }

        console.warn('未找到有效数值，返回默认值0');
        return 0;
    }

    /**
     * 从数组数据中提取文本内容（用于文本组件）
     */
    extractTextContent(labels, values, labelField, valueField) {
        console.log('提取文本内容:', {
            labelsLength: labels ? labels.length : 0,
            valuesLength: values ? values.length : 0,
            labelField: labelField,
            valueField: valueField
        });

        // 如果没有数据，返回默认文本
        if ((!labels || labels.length === 0) && (!values || values.length === 0)) {
            console.warn('标签和数值数组都为空，返回默认文本');
            return '无数据';
        }

        // 优先使用数值字段的内容（如果用户选择了数值字段）
        if (valueField && values && values.length > 0) {
            // 使用最后一个值（最新数据）
            const lastValue = values[values.length - 1];
            if (lastValue !== null && lastValue !== undefined) {
                const textContent = lastValue.toString();
                console.log('使用数值字段内容:', textContent, '来源: 最后一个数值');
                return textContent;
            }
        }

        // 回退到标签字段的内容
        if (labelField && labels && labels.length > 0) {
            // 使用最后一个标签（最新数据）
            const lastLabel = labels[labels.length - 1];
            if (lastLabel !== null && lastLabel !== undefined) {
                const textContent = lastLabel.toString();
                console.log('使用标签字段内容:', textContent, '来源: 最后一个标签');
                return textContent;
            }
        }

        // 如果没有指定字段，智能选择内容
        if (values && values.length > 0) {
            const lastValue = values[values.length - 1];
            if (lastValue !== null && lastValue !== undefined) {
                const textContent = lastValue.toString();
                console.log('使用默认数值内容:', textContent);
                return textContent;
            }
        }

        if (labels && labels.length > 0) {
            const lastLabel = labels[labels.length - 1];
            if (lastLabel !== null && lastLabel !== undefined) {
                const textContent = lastLabel.toString();
                console.log('使用默认标签内容:', textContent);
                return textContent;
            }
        }

        console.warn('未找到有效的文本内容，返回默认文本');
        return '无数据';
    }

    /**
     * 转换原始数据为图表格式
     */
    convertRawDataToChartFormat(rawData, chartType, dataSourceConfig = {}) {
        // 如果是原始数据数组，尝试提取第一列作为标签，第二列作为数值
        const labels = [];
        const values = [];

        rawData.forEach((row, index) => {
            if (typeof row === 'object' && row !== null) {
                const keys = Object.keys(row);
                if (keys.length >= 2) {
                    labels.push(row[keys[0]] || `项目${index + 1}`);
                    values.push(parseFloat(row[keys[1]]) || 0);
                } else if (keys.length === 1) {
                    labels.push(`项目${index + 1}`);
                    values.push(parseFloat(row[keys[0]]) || 0);
                }
            } else {
                labels.push(`项目${index + 1}`);
                values.push(parseFloat(row) || 0);
            }
        });

        // 为仪表盘、水波图和柱状百分比图提供数值格式
        if (chartType === 'gauge-chart' || chartType === 'water-chart' || chartType === 'column-percentage-chart') {
            const singleValue = this.extractSingleValue(labels, values);
            return {
                success: true,
                value: singleValue,
                unit: '',
                name: '外部数据'
            };
        }

        // 为文本组件提供特殊格式
        if (chartType === 'text-label') {
            const textContent = this.extractTextContent(labels, values, null, null);
            return {
                success: true,
                textContent: textContent,
                labels: labels,
                values: values
            };
        }

        // 为图表组件提供标准格式
        const result = {
            success: true,
            labels: labels,
            values: values
        };

        // 为饼图添加特殊格式
        if (chartType === 'pie-chart') {
            result.data = labels.map((label, index) => ({
                name: label,
                value: values[index] || 0
            }));
        }

        // 为多折线图添加特殊格式
        if (chartType === 'multi-line-chart') {
            const dataSetName = dataSourceConfig.dataSetName || '外部数据';
            result.xAxis = labels;
            result.series = [{
                name: dataSetName,
                type: 'line',
                data: values,
                yAxisIndex: 0
            }];
            console.log('多折线图原始数据转换:', {
                dataSetName: dataSetName,
                xAxis: result.xAxis,
                series: result.series
            });
        }

        return result;
    }

    /**
     * 合并多外部数据源数据
     */
    mergeMultiExternalData(results, mergeStrategy, componentType) {
        try {
            console.log('合并多外部数据源数据:', { results, mergeStrategy, componentType });

            // 过滤成功的结果
            const successResults = results.filter(result => result.success && result.data);
            const failedResults = results.filter(result => !result.success);

            if (successResults.length === 0) {
                const errorMessages = failedResults.map(r => `${r.alias}: ${r.error}`).join('; ');
                return this.getErrorData(`所有数据集查询失败: ${errorMessages}`);
            }

            // 记录失败的数据集
            if (failedResults.length > 0) {
                console.warn('部分数据集查询失败:', failedResults);
            }

            // 根据组件类型和合并策略处理数据
            if (componentType === 'data-table') {
                return this.mergeTableData(successResults, mergeStrategy);
            } else {
                return this.mergeChartData(successResults, mergeStrategy, componentType);
            }

        } catch (error) {
            console.error('合并多外部数据源数据失败:', error);
            return this.getErrorData('数据合并失败: ' + error.message);
        }
    }

    /**
     * 合并表格数据
     */
    mergeTableData(results, mergeStrategy) {
        const mergedData = [];
        const allColumns = new Set();

        // 收集所有列名
        results.forEach(result => {
            if (result.data && Array.isArray(result.data)) {
                result.data.forEach(row => {
                    Object.keys(row).forEach(key => allColumns.add(key));
                });
            }
        });

        // 根据合并策略处理数据
        if (mergeStrategy === 'union') {
            // 联合：合并所有行
            results.forEach((result, index) => {
                if (result.data && Array.isArray(result.data)) {
                    result.data.forEach(row => {
                        const newRow = { ...row };
                        // 添加数据源标识
                        newRow['_dataSource'] = result.alias || `数据集${index + 1}`;
                        mergedData.push(newRow);
                    });
                }
            });
        } else if (mergeStrategy === 'separate') {
            // 分离：每个数据集作为独立部分
            results.forEach((result, index) => {
                if (result.data && Array.isArray(result.data)) {
                    // 添加分隔行
                    if (index > 0) {
                        const separatorRow = {};
                        Array.from(allColumns).forEach(col => {
                            separatorRow[col] = '---';
                        });
                        mergedData.push(separatorRow);
                    }

                    // 添加数据集标题行
                    const titleRow = {};
                    Array.from(allColumns).forEach((col, colIndex) => {
                        titleRow[col] = colIndex === 0 ? (result.alias || `数据集${index + 1}`) : '';
                    });
                    mergedData.push(titleRow);

                    // 添加数据行
                    result.data.forEach(row => {
                        mergedData.push({ ...row });
                    });
                }
            });
        }

        return {
            success: true,
            data: mergedData,
            columns: Array.from(allColumns),
            message: `成功合并 ${results.length} 个数据集`
        };
    }

    /**
     * 合并图表数据
     */
    mergeChartData(results, mergeStrategy, componentType) {
        const mergedLabels = [];
        const mergedValues = [];
        const mergedData = [];

        if (mergeStrategy === 'union') {
            // 联合：合并所有数据点
            results.forEach((result, index) => {
                const data = result.data;
                if (data && data.labels && data.values) {
                    data.labels.forEach((label, i) => {
                        const value = data.values[i];
                        const alias = result.alias || `数据集${index + 1}`;

                        mergedLabels.push(`${label} (${alias})`);
                        mergedValues.push(value);

                        if (componentType === 'pie-chart') {
                            mergedData.push({
                                name: `${label} (${alias})`,
                                value: value
                            });
                        }
                    });
                }
            });
        } else if (mergeStrategy === 'separate') {
            // 分离：每个数据集保持独立
            results.forEach((result, index) => {
                const data = result.data;
                if (data && data.labels && data.values) {
                    const alias = result.alias || `数据集${index + 1}`;

                    // 添加数据集分隔标识
                    if (index > 0) {
                        mergedLabels.push('---');
                        mergedValues.push(0);
                    }

                    // 添加数据集标题
                    mergedLabels.push(alias);
                    mergedValues.push(0);

                    // 添加数据
                    data.labels.forEach((label, i) => {
                        const value = data.values[i];
                        mergedLabels.push(label);
                        mergedValues.push(value);

                        if (componentType === 'pie-chart') {
                            mergedData.push({
                                name: label,
                                value: value
                            });
                        }
                    });
                }
            });
        }

        // 为多折线图添加特殊格式
        if (componentType === 'multi-line-chart') {
            const result = this.mergeMultiLineChartData(results, mergeStrategy);
            console.log('多折线图合并结果:', result);
            return result;
        }

        const result = {
            success: true,
            labels: mergedLabels,
            values: mergedValues,
            message: `成功合并 ${results.length} 个数据集`
        };

        if (componentType === 'pie-chart') {
            result.data = mergedData;
        }

        console.log('合并后的图表数据:', result);
        return result;
    }

    /**
     * 合并多折线图数据
     */
    mergeMultiLineChartData(results, mergeStrategy) {
        try {
            console.log('合并多折线图数据:', { results, mergeStrategy });

            // 收集所有X轴数据（时间点）
            const allXAxisData = new Set();
            const seriesData = [];

            results.forEach((result, index) => {
                const data = result.data;
                if (data) {
                    // 处理外部数据源的标准格式 - 适配数据集接口规范
                    let labels, values, seriesName;

                    if (data.labels && data.values) {
                        // 外部数据源标准格式：{labels: [], values: []}
                        labels = data.labels;
                        values = data.values;
                        // 使用数据集别名作为系列名称（图例显示）
                        seriesName = result.alias || `数据集${index + 1}`;

                        console.log(`处理外部数据集 ${index + 1}:`, {
                            alias: result.alias,
                            seriesName: seriesName,
                            labelsCount: labels.length,
                            valuesCount: values.length,
                            sampleLabels: labels.slice(0, 3),
                            sampleValues: values.slice(0, 3)
                        });

                    } else if (data.xAxis && data.series && data.series.length > 0) {
                        // 已经是多折线格式，取第一个系列
                        labels = data.xAxis;
                        values = data.series[0].data;
                        seriesName = result.alias || data.series[0].name || `数据集${index + 1}`;

                        console.log(`处理多折线格式数据集 ${index + 1}:`, {
                            seriesName: seriesName,
                            labelsCount: labels.length,
                            valuesCount: values.length
                        });

                    } else {
                        console.warn(`无法识别的数据格式 (数据集 ${index + 1}):`, data);
                        return;
                    }

                    // 收集所有时间点到X轴数据集合
                    labels.forEach(label => allXAxisData.add(label));

                    // 为每个数据集创建独立的折线系列
                    seriesData.push({
                        name: seriesName,           // 使用别名作为系列名称
                        type: 'line',
                        data: values,
                        yAxisIndex: 0,
                        originalLabels: labels,
                        alias: result.alias         // 保留别名信息
                    });
                }
            });

            // 转换为数组并排序
            const xAxisArray = Array.from(allXAxisData).sort();

            // 对齐数据：确保每个系列的数据与统一的X轴对应，使用智能填充避免折线中断
            const alignedSeries = seriesData.map(series => {
                const alignedData = [];
                let lastValidValue = null;

                for (let i = 0; i < xAxisArray.length; i++) {
                    const xLabel = xAxisArray[i];
                    const index = series.originalLabels.indexOf(xLabel);

                    if (index >= 0) {
                        // 找到对应数据点
                        const value = series.data[index];
                        alignedData.push(value);
                        lastValidValue = value; // 更新最后有效值
                    } else {
                        // 没有对应数据点，使用智能填充策略
                        if (lastValidValue !== null) {
                            // 使用前一个有效值填充，保持折线连续
                            alignedData.push(lastValidValue);
                            console.log(`数据集 ${series.name} 在时间点 ${xLabel} 缺失数据，使用前值填充: ${lastValidValue}`);
                        } else {
                            // 如果还没有有效值，使用null（这种情况通常出现在序列开始部分）
                            alignedData.push(null);
                        }
                    }
                }

                return {
                    name: series.name,          // 系列名称 = 数据集别名
                    type: 'line',
                    data: alignedData,
                    yAxisIndex: series.yAxisIndex,
                    alias: series.alias         // 保留别名信息
                };
            });

            // 构建图例数据：每个数据集别名对应一个图例项
            const legendData = alignedSeries.map(series => series.name);

            // 检查数据量并进行性能优化
            const totalDataPoints = xAxisArray.length;
            const maxDataPoints = 1000; // 设置最大数据点阈值

            let finalXAxis = xAxisArray;
            let finalSeries = alignedSeries;
            let performanceMessage = '';

            if (totalDataPoints > maxDataPoints) {
                console.warn(`多数据集数据量过大 (${totalDataPoints} 个数据点)，启用数据采样优化`);

                // 智能采样：保持数据趋势
                const samplingResult = this.smartDataSampling(xAxisArray, alignedSeries, maxDataPoints);
                finalXAxis = samplingResult.xAxis;
                finalSeries = samplingResult.series;

                performanceMessage = ` (已优化：${totalDataPoints} → ${finalXAxis.length} 个数据点)`;
                console.log(`多数据集采样完成：${totalDataPoints} → ${finalXAxis.length} 个数据点`);
            }

            const result = {
                success: true,
                xAxis: finalXAxis,              // X轴标签 = 合并的时间轴
                series: finalSeries,            // 系列数据 = 每个数据集一条折线
                legendData: legendData,         // 图例数据 = 数据集别名数组
                message: `成功合并 ${results.length} 个数据集为多折线图${performanceMessage}`,
                originalDataPoints: totalDataPoints,
                optimizedDataPoints: finalXAxis.length,
                isOptimized: totalDataPoints > maxDataPoints,
                dataSetCount: results.length    // 数据集数量 = 折线数量
            };

            console.log('多折线图多数据集合并完成:', {
                dataSetCount: result.dataSetCount,
                seriesCount: result.series.length,
                legendCount: result.legendData.length,
                xAxisLength: result.xAxis.length,
                legendData: result.legendData,
                isOptimized: result.isOptimized
            });

            return result;

        } catch (error) {
            console.error('合并多折线图数据失败:', error);
            return this.getErrorData('多折线图数据合并失败: ' + error.message);
        }
    }

    /**
     * 智能数据采样 - 保持数据趋势的同时减少数据点
     */
    smartDataSampling(xAxisArray, seriesArray, maxPoints) {
        try {
            const totalPoints = xAxisArray.length;
            if (totalPoints <= maxPoints) {
                return { xAxis: xAxisArray, series: seriesArray };
            }

            console.log(`开始智能采样：${totalPoints} → ${maxPoints} 个数据点`);

            // 计算采样间隔
            const interval = Math.floor(totalPoints / maxPoints);
            const sampledXAxis = [];
            const sampledSeries = seriesArray.map(series => ({
                ...series,
                data: []
            }));

            // 均匀采样策略：保持数据分布
            for (let i = 0; i < totalPoints; i += interval) {
                // 确保包含第一个和最后一个数据点
                if (i === 0 || i >= totalPoints - interval || sampledXAxis.length < maxPoints) {
                    sampledXAxis.push(xAxisArray[i]);

                    // 为每个系列采样对应的数据点
                    seriesArray.forEach((series, seriesIndex) => {
                        sampledSeries[seriesIndex].data.push(series.data[i]);
                    });
                }

                // 防止超过最大点数
                if (sampledXAxis.length >= maxPoints) {
                    break;
                }
            }

            // 确保包含最后一个数据点
            if (sampledXAxis.length > 0 && sampledXAxis[sampledXAxis.length - 1] !== xAxisArray[totalPoints - 1]) {
                sampledXAxis.push(xAxisArray[totalPoints - 1]);
                seriesArray.forEach((series, seriesIndex) => {
                    sampledSeries[seriesIndex].data.push(series.data[totalPoints - 1]);
                });
            }

            console.log(`采样完成：实际采样 ${sampledXAxis.length} 个数据点`);
            return { xAxis: sampledXAxis, series: sampledSeries };

        } catch (error) {
            console.error('数据采样失败:', error);
            // 降级处理：简单截取前N个数据点
            return {
                xAxis: xAxisArray.slice(0, maxPoints),
                series: seriesArray.map(series => ({
                    ...series,
                    data: series.data.slice(0, maxPoints)
                }))
            };
        }
    }

    /**
     * 获取错误数据
     */
    getErrorData(message) {
        return {
            success: false,
            message: message,
            labels: ['错误'],
            values: [0],
            data: [{ name: '错误', value: 0 }]
        };
    }

    /**
     * 处理状态指示器组件数据
     */
    async processStatusIndicatorData(widget, dataSourceConfig) {
        console.log('=== 处理状态指示器数据 ===');
        console.log('数据源配置:', dataSourceConfig);

        try {
            // 如果没有配置数据源，返回示例数据
            if (!dataSourceConfig || !dataSourceConfig.deviceId || !dataSourceConfig.dataItemId) {
                console.log('状态指示器未配置数据源，返回示例数据');
                return {
                    success: true,
                    data: [{
                        value: Math.floor(Math.random() * 100),
                        timestamp: new Date().toISOString()
                    }],
                    deviceOffline: Math.random() < 0.1, // 10%概率模拟设备离线
                    message: '使用示例数据'
                };
            }

            // 获取设备状态
            const deviceStatus = await this.getDeviceStatus(dataSourceConfig.deviceId);
            console.log('设备状态:', deviceStatus);

            // 如果设备离线，直接返回离线状态
            if (!deviceStatus.connected) {
                console.log('设备离线，返回离线状态');
                return {
                    success: true,
                    data: [],
                    deviceOffline: true,
                    message: '设备离线'
                };
            }

            // 获取监控项数据
            const dataResult = await this.processDataItemData(widget, dataSourceConfig);
            console.log('监控项数据结果:', dataResult);

            if (dataResult.success) {
                // 为状态指示器添加设备状态信息
                dataResult.deviceOffline = false;
                dataResult.deviceStatus = deviceStatus;
            }

            return dataResult;

        } catch (error) {
            console.error('处理状态指示器数据失败:', error);
            return {
                success: false,
                error: error.message || '数据获取失败',
                deviceOffline: true
            };
        }
    }

    /**
     * 获取设备状态
     */
    async getDeviceStatus(deviceId) {
        try {
            const response = await fetch(`/api/device/${deviceId}`);
            if (!response.ok) {
                throw new Error(`获取设备状态失败: ${response.status}`);
            }
            const device = await response.json();
            return {
                connected: device.connected || false,
                name: device.name,
                address: device.address,
                port: device.port
            };
        } catch (error) {
            console.error('获取设备状态失败:', error);
            return {
                connected: false,
                name: '未知设备',
                address: '',
                port: 0
            };
        }
    }
}

/**
 * 标准化的BiDataSourceManager初始化函数
 * 统一不同页面的初始化方式
 */
function initializeBiDataSourceManager(options = {}) {
    const defaultOptions = {
        enableLogging: true,
        enableValidation: true,
        enableContextManagement: true
    };

    const finalOptions = { ...defaultOptions, ...options };

    try {
        if (typeof BiDataSourceManager !== 'undefined') {
            // 如果已经存在实例，先清理
            if (window.biDataSourceManager) {
                // 清理所有组件上下文
                if (window.biDataSourceManager.componentContexts) {
                    window.biDataSourceManager.componentContexts.clear();
                }
                if (window.biDataSourceManager.validationCache) {
                    window.biDataSourceManager.validationCache.clear();
                }
            }

            // 创建新实例
            window.biDataSourceManager = new BiDataSourceManager();

            if (finalOptions.enableLogging) {
                console.log('BiDataSourceManager初始化成功', {
                    supportedTypes: window.biDataSourceManager.supportedTypes,
                    componentTypes: Object.keys(window.biDataSourceManager.componentConfigs),
                    enableValidation: finalOptions.enableValidation,
                    enableContextManagement: finalOptions.enableContextManagement
                });
            }

            return {
                success: true,
                instance: window.biDataSourceManager,
                message: 'BiDataSourceManager初始化成功'
            };
        } else {
            const error = 'BiDataSourceManager类未定义，请检查bi-data-source-manager.js是否正确加载';
            console.error(error);
            return {
                success: false,
                instance: null,
                message: error
            };
        }
    } catch (error) {
        const errorMessage = `BiDataSourceManager初始化失败: ${error.message}`;
        console.error(errorMessage, error);
        return {
            success: false,
            instance: null,
            message: errorMessage,
            error: error
        };
    }
}

/**
 * 检查BiDataSourceManager是否可用
 */
function checkBiDataSourceManagerAvailability() {
    return {
        isClassDefined: typeof BiDataSourceManager !== 'undefined',
        isInstanceAvailable: window.biDataSourceManager instanceof BiDataSourceManager,
        instanceMethods: window.biDataSourceManager ? Object.getOwnPropertyNames(Object.getPrototypeOf(window.biDataSourceManager)) : [],
        supportedTypes: window.biDataSourceManager ? window.biDataSourceManager.supportedTypes : []
    };
}

// 创建全局实例（保持向后兼容）
if (typeof BiDataSourceManager !== 'undefined') {
    window.biDataSourceManager = new BiDataSourceManager();
} else {
    console.warn('BiDataSourceManager类未定义，将在页面加载完成后尝试初始化');
}
