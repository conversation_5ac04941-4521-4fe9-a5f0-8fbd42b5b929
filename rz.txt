[新] 使用 Edge 中的 Copilot 来解释控制台错误: 单击
         
         以说明错误。
        了解更多信息
        不再显示
bi-widget-configs.js?v=20250730-remove-shapes:1844  未找到组件配置: data-table
getWidgetDefaultConfig @ bi-widget-configs.js?v=20250730-remove-shapes:1844
createStandardWidget @ bi-dashboard-designer.js?v=20250731-export-debug:721
createWidget @ bi-dashboard-designer.js?v=20250731-export-debug:830
dropWidget @ bi-dashboard-designer.js?v=20250731-export-debug:437
ondrop @ design:585
bi-widget-configs.js?v=20250730-remove-shapes:1854  未找到组件配置选项: data-table
getWidgetConfigOptions @ bi-widget-configs.js?v=20250730-remove-shapes:1854
createStandardWidget @ bi-dashboard-designer.js?v=20250731-export-debug:731
createWidget @ bi-dashboard-designer.js?v=20250731-export-debug:830
dropWidget @ bi-dashboard-designer.js?v=20250731-export-debug:437
ondrop @ design:585
bi-dashboard-designer.js?v=20250731-export-debug:807 转换组件 3440 从标准格式: {setupKeys: Array(0), configKeys: Array(2), styleConfigKeys: Array(0)}
bi-dashboard-designer.js?v=20250731-export-debug:952 开始渲染组件 3440，类型: data-table
bi-dashboard-designer.js?v=20250731-export-debug:973 渲染组件 3440 DOM zIndex: 1016
bi-dashboard-designer.js?v=20250731-export-debug:1085 渲染表格组件 3440，列配置: (3) ['设备', '状态', '数值']
bi-dashboard-designer.js?v=20250731-export-debug:1086 表格组件完整配置: {title: undefined, chartType: undefined}
bi-dashboard-designer.js?v=20250731-export-debug:1097 表格组件 3440 HTML: 
                <table class="table table-sm">
                    <thead>
                        <tr><th>设备</th><th>状态</th><th>数值</th></tr>
                    </thead>
                    <tbody>
                        <tr><td>示例数据</td><td>正常</td><td>100</td></tr>
                    </tbody>
                </table>
            
bi-dashboard-designer.js?v=20250731-export-debug:1029 === 应用组件 3440 样式配置 ===
bi-dashboard-designer.js?v=20250731-export-debug:1030 样式配置: {}
bi-dashboard-designer.js?v=20250731-export-debug:1059 组件 3440 显示标题栏
bi-dashboard-designer.js?v=20250731-export-debug:1063 === 组件 3440 样式配置应用完成 ===
bi-dashboard-designer.js?v=20250731-export-debug:4042 缓存组件 3440 配置，时间戳: 2025-08-01T06:08:57.156Z
bi-dashboard-designer.js?v=20250731-export-debug:4049 获取组件 3440 缓存配置，时间戳: 2025-08-01T06:08:57.156Z
bi-dashboard-designer.js?v=20250731-export-debug:4075  组件 3440 配置缓存不一致: {configMatch: true, styleMatch: true, stateMatch: false, cached: {…}, current: {…}}
validateConfigCacheConsistency @ bi-dashboard-designer.js?v=20250731-export-debug:4075
updatePropertyPanel @ bi-dashboard-designer.js?v=20250731-export-debug:2733
selectWidget @ bi-dashboard-designer.js?v=20250731-export-debug:2718
createWidget @ bi-dashboard-designer.js?v=20250731-export-debug:842
dropWidget @ bi-dashboard-designer.js?v=20250731-export-debug:437
ondrop @ design:585
bi-dashboard-designer.js?v=20250731-export-debug:4091 修复组件 3440 配置缓存不一致
bi-dashboard-designer.js?v=20250731-export-debug:4042 缓存组件 3440 配置，时间戳: 2025-08-01T06:08:57.156Z
bi-dashboard-designer.js?v=20250731-export-debug:532 同步组件 3440 配置缓存
bi-dashboard-designer.js?v=20250731-export-debug:481 修复组件 3440 的修改状态：应为已修改但当前为未修改
bi-dashboard-designer.js?v=20250731-export-debug:464 组件 3440 (data-table) 已标记为修改: state_correction
bi-dashboard-designer.js?v=20250731-export-debug:4042 缓存组件 3440 配置，时间戳: 2025-08-01T06:08:57.156Z
bi-dashboard-designer.js?v=20250731-export-debug:532 同步组件 3440 配置缓存
bi-dashboard-designer.js?v=20250731-export-debug:6432 从服务器加载设备列表
bi-data-source-manager.js?v=20250127:558 恢复数据源配置，组件信息: {id: 3440, type: 'data-table', widgetType: undefined}
bi-data-source-manager.js?v=20250127:591 属性面板加载中，跳过onDataSourceTypeChange调用以保护设备选择
bi-dashboard-designer.js?v=20250731-export-debug:7924 重置所有配置项的显示状态
bi-dashboard-designer.js?v=20250731-export-debug:7956 多折线图配置显示状态已重置
bi-dashboard-designer.js?v=20250731-export-debug:7959 配置项显示状态重置完成
bi-dashboard-designer.js?v=20250731-export-debug:9702 实时配置监听器已设置，跳过重复设置
bi-dashboard-designer.js?v=20250731-export-debug:14066 === 更新图层列表 ===
bi-dashboard-designer.js?v=20250731-export-debug:14067 组件zIndex排序: [{…}]
bi-dashboard-designer.js?v=20250731-export-debug:14068 === 图层列表更新完成 ===
bi-dashboard-designer.js?v=20250731-export-debug:4042 缓存组件 3440 配置，时间戳: 2025-08-01T06:08:57.157Z
bi-dashboard-designer.js?v=20250731-export-debug:12752 组件 3440 自动刷新条件检查: {dataSourceType: 'static', hasDataItemId: false, hasDataSetId: false, isMultiDataSet: false, dataSetsCount: 0, …}
bi-dashboard-designer.js?v=20250731-export-debug:12800 设置组件 3440 静态数据源一次性更新 (数据源类型: static)
bi-dashboard-designer.js?v=20250731-export-debug:14066 === 更新图层列表 ===
bi-dashboard-designer.js?v=20250731-export-debug:14067 组件zIndex排序: [{…}]
bi-dashboard-designer.js?v=20250731-export-debug:14068 === 图层列表更新完成 ===
bi-dashboard-designer.js?v=20250731-export-debug:859 创建组件 (标准化): {type: 'data-table', value: {…}, options: Array(0)}
bi-dashboard-designer.js?v=20250731-export-debug:860 创建组件 (兼容格式): {id: 3440, type: 'data-table', x: 116, y: 318, width: 300, …}
bi-dashboard-designer.js?v=20250731-export-debug:12860 安全更新组件数据: 3440 data-table
bi-dashboard-designer.js?v=20250731-export-debug:10030 更新组件数据: 3440 data-table
bi-data-source-manager.js?v=20250127:913 === BiDataSourceManager - 开始获取组件数据 ===
bi-data-source-manager.js?v=20250127:914 组件信息: {id: 3440, type: 'data-table', dataSourceConfig: '{"dataSourceType":"static","dataItemId":null,"data…d":null,"valueField":null,"refreshInterval":5000}'}
bi-data-source-manager.js?v=20250127:959 解析字符串格式数据源配置: {dataSourceType: 'static', dataItemId: null, dataSetId: null, labelField: null, valueField: null, …}
bi-data-source-manager.js?v=20250127:974 确定的数据源类型: static
bi-data-source-manager.js?v=20250127:975 完整数据源配置: {dataSourceType: 'static', dataItemId: null, dataSetId: null, labelField: null, valueField: null, …}
bi-data-source-manager.js?v=20250127:982 处理静态数据源...
bi-data-source-manager.js?v=20250127:1016 处理静态数据: {dataSourceType: 'static', dataItemId: null, dataSetId: null, labelField: null, valueField: null, …}
bi-data-source-manager.js?v=20250127:1335 解析静态数据: {labels: Array(0), values: Array(0)}
bi-data-source-manager.js?v=20250127:1339  静态数据为空
parseStaticData @ bi-data-source-manager.js?v=20250127:1339
processStaticData @ bi-data-source-manager.js?v=20250127:1017
fetchWidgetData @ bi-data-source-manager.js?v=20250127:983
updateWidgetData @ bi-dashboard-designer.js?v=20250731-export-debug:10035
updateWidgetDataSafe @ bi-dashboard-designer.js?v=20250731-export-debug:12871
waitForChartInitStatic @ bi-dashboard-designer.js?v=20250731-export-debug:12818
setTimeout
setupWidgetAutoRefresh @ bi-dashboard-designer.js?v=20250731-export-debug:12823
createWidget @ bi-dashboard-designer.js?v=20250731-export-debug:854
dropWidget @ bi-dashboard-designer.js?v=20250731-export-debug:437
ondrop @ design:585
bi-data-source-manager.js?v=20250127:1020  静态数据解析失败
processStaticData @ bi-data-source-manager.js?v=20250127:1020
fetchWidgetData @ bi-data-source-manager.js?v=20250127:983
updateWidgetData @ bi-dashboard-designer.js?v=20250731-export-debug:10035
updateWidgetDataSafe @ bi-dashboard-designer.js?v=20250731-export-debug:12871
waitForChartInitStatic @ bi-dashboard-designer.js?v=20250731-export-debug:12818
setTimeout
setupWidgetAutoRefresh @ bi-dashboard-designer.js?v=20250731-export-debug:12823
createWidget @ bi-dashboard-designer.js?v=20250731-export-debug:854
dropWidget @ bi-dashboard-designer.js?v=20250731-export-debug:437
ondrop @ design:585
bi-data-source-manager.js?v=20250127:1000 数据获取结果: {success: false, error: '静态数据解析失败'}
bi-data-source-manager.js?v=20250127:1001 === BiDataSourceManager - 数据获取完成 ===
bi-dashboard-designer.js?v=20250731-export-debug:12819 组件 3440 静态数据源更新完成
bi-dashboard-designer.js?v=20250731-export-debug:10044  数据获取失败: 静态数据解析失败
updateWidgetData @ bi-dashboard-designer.js?v=20250731-export-debug:10044
await in updateWidgetData
updateWidgetDataSafe @ bi-dashboard-designer.js?v=20250731-export-debug:12871
waitForChartInitStatic @ bi-dashboard-designer.js?v=20250731-export-debug:12818
setTimeout
setupWidgetAutoRefresh @ bi-dashboard-designer.js?v=20250731-export-debug:12823
createWidget @ bi-dashboard-designer.js?v=20250731-export-debug:854
dropWidget @ bi-dashboard-designer.js?v=20250731-export-debug:437
ondrop @ design:585
bi-dashboard-designer.js?v=20250731-export-debug:3804 属性面板加载完成，清除加载标志位
bi-dashboard-designer.js?v=20250731-export-debug:15320 初始化组件 3440 (data-table) 的多数据集配置，容器ID: tableExternalDataSourceList
bi-dashboard-designer.js?v=20250731-export-debug:15330 清理容器 tableExternalDataSourceList 的内容
bi-dashboard-designer.js?v=20250731-export-debug:15494 在容器 tableExternalDataSourceList 中创建新的数据集项: externalDataSet_0
bi-dashboard-designer.js?v=20250731-export-debug:464 组件 3440 (data-table) 已标记为修改: properties
bi-dashboard-designer.js?v=20250731-export-debug:4042 缓存组件 3440 配置，时间戳: 2025-08-01T06:08:59.972Z
bi-dashboard-designer.js?v=20250731-export-debug:532 同步组件 3440 配置缓存
bi-data-source-manager.js?v=20250127:179 为组件 3440 (data-table) 创建配置上下文
bi-data-source-manager.js?v=20250127:249 为组件 3440 缓存DOM元素: dataSourceType
bi-data-source-manager.js?v=20250127:1451 收集组件 3440 (data-table) 外部数据源配置，多数据集模式: true
bi-data-source-manager.js?v=20250127:1513 收集多数据集配置 - 容器ID: tableExternalDataSourceList
bi-data-source-manager.js?v=20250127:1514 收集多数据集配置 - 找到数据集项数量: 1
bi-data-source-manager.js?v=20250127:1523 数据集项 DOM索引0: {itemId: 'externalDataSet_0', dataSetId: '', dataSetName: '加载中...', labelField: 'null', valueField: 'null', …}
bi-data-source-manager.js?v=20250127:1567 跳过数据集项 DOM索引0: 未选择数据集
bi-data-source-manager.js?v=20250127:1571 最终收集到的数据集配置: []
bi-data-source-manager.js?v=20250127:1456 组件 3440 收集到多数据集配置，数据集数量: 0
bi-data-source-manager.js?v=20250127:429 组件 3440 数据源配置收集完成: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(0), refreshInterval: 5, mergeStrategy: 'union'}
bi-dashboard-designer.js?v=20250731-export-debug:11789 === 开始收集样式配置 ===
bi-dashboard-designer.js?v=20250731-export-debug:11405 🔧 [修复版本 v2.0] 开始收集多折线样式配置
bi-dashboard-designer.js?v=20250731-export-debug:11412 🔧 [修复版本] 折线数量: 1 (来源: 实际数据 )
bi-dashboard-designer.js?v=20250731-export-debug:11423 收集折线 1 的样式配置
bi-dashboard-designer.js?v=20250731-export-debug:11457 收集到的多折线样式配置: [{…}]
bi-dashboard-designer.js?v=20250731-export-debug:12484 === 样式配置收集完成 ===
bi-dashboard-designer.js?v=20250731-export-debug:12485 收集到的配置项数量: 169
bi-dashboard-designer.js?v=20250731-export-debug:12486 重要配置项检查: {showDataLabels: false, primaryColor: undefined, theme: 'default', showChartTitle: true, showLegend: true, …}
bi-dashboard-designer.js?v=20250731-export-debug:12502 完整样式配置: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250731-export-debug:4670 === 实时应用属性：样式配置更新 ===
bi-dashboard-designer.js?v=20250731-export-debug:4671 组件ID: 3440
bi-dashboard-designer.js?v=20250731-export-debug:4672 组件类型: data-table
bi-dashboard-designer.js?v=20250731-export-debug:4673 更新前styleConfig: {}
bi-dashboard-designer.js?v=20250731-export-debug:4674 新的styleConfig: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250731-export-debug:4683 更新后styleConfig字符串: {"theme":"default","backgroundColor":"#ffffff","borderColor":"#dee2e6","showTitle":true,"transparent":false,"titleFontSize":16,"showLegend":true,"showChartTitle":true,"borderlessMode":false,"showDataLabels":false,"showYAxis":true,"showYAxisLabels":true,"showXAxis":true,"yAxisFontSize":12,"yAxisFontColor":"#8c8c8c","xAxisFontSize":12,"xAxisFontColor":"#8c8c8c","dataLabelFontSize":11,"dataLabelFontColor":"#5470c6","legendFontSize":12,"legendFontColor":"#666666","lineWidth":3,"lineType":"solid","lineColor":"#5470c6","useGradientLine":false,"lineGradientColor":"#91cc75","smoothLine":true,"showArea":false,"useGradientArea":true,"areaStartColor":"#5470c6","areaMidColor":"#91cc75","areaEndColor":"#ffffff","showSymbol":true,"symbolType":"circle","symbolSize":8,"symbolColor":"#5470c6","enableShadow":false,"shadowBlur":10,"shadowOffsetY":3,"shadowColor":"#000000","multiLineSmooth":true,"multiLineShowSymbol":true,"multiLineSymbolSize":6,"multiLineWidth":2,"multiLineShowArea":false,"multiLineEnableDualYAxis":false,"useIndividualStyles":true,"lineCount":1,"individualLineStyles":[{"color":"#5470c6","width":2,"type":"solid","useGradient":false,"gradientColor":"#91cc75","showSymbol":true,"symbolSize":6,"symbolType":"circle","symbolColor":"#5470c6","symbolBorderColor":"#ffffff","symbolBorderWidth":0,"showArea":false,"areaOpacity":30,"showLabel":false,"labelPosition":"top","labelColor":"#333333","labelFontSize":12,"labelFontWeight":"normal"}],"barWidth":60,"borderRadius":0,"borderRadiusMode":"top","barColor":"#5470c6","useGradientBar":false,"barStartColor":"#5470c6","barEndColor":"#91cc75","gaugeMin":0,"gaugeMax":100,"gaugeUnit":"%","gaugeRadius":80,"pieType":"ring","pieRadius":70,"pieInnerRadius":40,"pieOuterRadius":70,"pieBorderRadius":0,"pieBorderRadiusMode":"all","showPieBorder":true,"pieShowDataLabels":true,"useCustomPieColors":false,"waterBorderWidth":5,"waterBorderColor":"#1890ff","useWaterBorderGradient":false,"waterBorderGradientColor":"#52c41a","waterWaveColor":"#1890ff","useWaterWaveGradient":false,"waterWaveGradientColor":"#52c41a","waterAmplitude":20,"showWaterLabel":true,"waterLabelFontSize":20,"waterLabelFontColor":"#333333","showPercentageSymbol":true,"columnBarWidth":40,"columnBorderRadius":8,"columnUseGradient":true,"columnStartColor":"#4facfe","columnEndColor":"#00f2fe","columnEnableShadow":true,"columnShadowColor":"#000000","columnShadowBlur":10,"columnShowDataLabel":true,"columnDataLabelPosition":"vertical-top","columnDataLabelColor":"#333333","columnDataLabelFontSize":14,"columnShowPercentage":true,"columnShowBarBackground":true,"columnBarBackgroundColor":"#f5f5f5","columnAllowOverflow":true,"textFontFamily":"Arial, sans-serif","textFontSize":16,"textFontColor":"#333333","textFontWeight":"normal","textFontStyle":"normal","textDecoration":"none","textAlign":"left","textVerticalAlign":"top","textLineHeight":1.5,"textLetterSpacing":0,"textPadding":10,"textBorderRadius":0,"enableTextGradient":false,"textGradientType":"linear","textGradientStartColor":"#ffffff","textGradientEndColor":"#f0f0f0","textGradientAngle":0,"imageUrl":"","imageBorderRadius":0,"imageRotation":0,"imageOpacity":100,"imageObjectFit":"contain","decorationObjectFit":"contain","decorationOpacity":100,"decorationRotation":0,"decorationBlendMode":"normal","tableStyle":"table","tableSize":"table-normal","showTableHeader":true,"tableHeaderBgColor":"#f8f9fa","tableHeaderTextColor":"#212529","tableBodyBgColor":"#ffffff","tableBodyTextColor":"#212529","tableBorderColor":"#dee2e6","tableHeaderFontSize":14,"tableBodyFontSize":12,"tableFontWeight":"normal","enableTableCarousel":false,"tableRowsPerPage":5,"tableCarouselInterval":3,"showTablePagination":false,"hyperlinkUrl":"","hyperlinkText":"点击跳转","hyperlinkTarget":"_self","hyperlinkFontSize":16,"hyperlinkFontColor":"#007bff","hyperlinkFontWeight":"normal","hyperlinkTextAlign":"left","hyperlinkFontStyle":"normal","hyperlinkTextDecoration":"none","hyperlinkOpacity":100,"htmlOpacity":100,"videoUrl":"","poster":"","autoplay":false,"loop":false,"muted":true,"controls":true,"opacity":100,"borderWidth":0,"shadow":false,"timeFormat":"YYYY-MM-DD HH:mm:ss","timeFontSize":24,"timeFontWeight":"normal","timeFontStyle":"normal","timeColorType":"solid","timeColor":"#333333","timeGradientStart":"#667eea","timeGradientEnd":"#764ba2","timeGradientDirection":"to right","timeOpacity":100,"timeTextShadow":false}
bi-dashboard-designer.js?v=20250731-export-debug:4684 === 样式配置更新完成 ===
bi-dashboard-designer.js?v=20250731-export-debug:11759 === 样式配置生命周期追踪：实时应用后 ===
bi-dashboard-designer.js?v=20250731-export-debug:11760 组件ID: 3440
bi-dashboard-designer.js?v=20250731-export-debug:11761 组件类型: data-table
bi-dashboard-designer.js?v=20250731-export-debug:11762 当前styleConfig: {"theme":"default","backgroundColor":"#ffffff","borderColor":"#dee2e6","showTitle":true,"transparent":false,"titleFontSize":16,"showLegend":true,"showChartTitle":true,"borderlessMode":false,"showDataLabels":false,"showYAxis":true,"showYAxisLabels":true,"showXAxis":true,"yAxisFontSize":12,"yAxisFontColor":"#8c8c8c","xAxisFontSize":12,"xAxisFontColor":"#8c8c8c","dataLabelFontSize":11,"dataLabelFontColor":"#5470c6","legendFontSize":12,"legendFontColor":"#666666","lineWidth":3,"lineType":"solid","lineColor":"#5470c6","useGradientLine":false,"lineGradientColor":"#91cc75","smoothLine":true,"showArea":false,"useGradientArea":true,"areaStartColor":"#5470c6","areaMidColor":"#91cc75","areaEndColor":"#ffffff","showSymbol":true,"symbolType":"circle","symbolSize":8,"symbolColor":"#5470c6","enableShadow":false,"shadowBlur":10,"shadowOffsetY":3,"shadowColor":"#000000","multiLineSmooth":true,"multiLineShowSymbol":true,"multiLineSymbolSize":6,"multiLineWidth":2,"multiLineShowArea":false,"multiLineEnableDualYAxis":false,"useIndividualStyles":true,"lineCount":1,"individualLineStyles":[{"color":"#5470c6","width":2,"type":"solid","useGradient":false,"gradientColor":"#91cc75","showSymbol":true,"symbolSize":6,"symbolType":"circle","symbolColor":"#5470c6","symbolBorderColor":"#ffffff","symbolBorderWidth":0,"showArea":false,"areaOpacity":30,"showLabel":false,"labelPosition":"top","labelColor":"#333333","labelFontSize":12,"labelFontWeight":"normal"}],"barWidth":60,"borderRadius":0,"borderRadiusMode":"top","barColor":"#5470c6","useGradientBar":false,"barStartColor":"#5470c6","barEndColor":"#91cc75","gaugeMin":0,"gaugeMax":100,"gaugeUnit":"%","gaugeRadius":80,"pieType":"ring","pieRadius":70,"pieInnerRadius":40,"pieOuterRadius":70,"pieBorderRadius":0,"pieBorderRadiusMode":"all","showPieBorder":true,"pieShowDataLabels":true,"useCustomPieColors":false,"waterBorderWidth":5,"waterBorderColor":"#1890ff","useWaterBorderGradient":false,"waterBorderGradientColor":"#52c41a","waterWaveColor":"#1890ff","useWaterWaveGradient":false,"waterWaveGradientColor":"#52c41a","waterAmplitude":20,"showWaterLabel":true,"waterLabelFontSize":20,"waterLabelFontColor":"#333333","showPercentageSymbol":true,"columnBarWidth":40,"columnBorderRadius":8,"columnUseGradient":true,"columnStartColor":"#4facfe","columnEndColor":"#00f2fe","columnEnableShadow":true,"columnShadowColor":"#000000","columnShadowBlur":10,"columnShowDataLabel":true,"columnDataLabelPosition":"vertical-top","columnDataLabelColor":"#333333","columnDataLabelFontSize":14,"columnShowPercentage":true,"columnShowBarBackground":true,"columnBarBackgroundColor":"#f5f5f5","columnAllowOverflow":true,"textFontFamily":"Arial, sans-serif","textFontSize":16,"textFontColor":"#333333","textFontWeight":"normal","textFontStyle":"normal","textDecoration":"none","textAlign":"left","textVerticalAlign":"top","textLineHeight":1.5,"textLetterSpacing":0,"textPadding":10,"textBorderRadius":0,"enableTextGradient":false,"textGradientType":"linear","textGradientStartColor":"#ffffff","textGradientEndColor":"#f0f0f0","textGradientAngle":0,"imageUrl":"","imageBorderRadius":0,"imageRotation":0,"imageOpacity":100,"imageObjectFit":"contain","decorationObjectFit":"contain","decorationOpacity":100,"decorationRotation":0,"decorationBlendMode":"normal","tableStyle":"table","tableSize":"table-normal","showTableHeader":true,"tableHeaderBgColor":"#f8f9fa","tableHeaderTextColor":"#212529","tableBodyBgColor":"#ffffff","tableBodyTextColor":"#212529","tableBorderColor":"#dee2e6","tableHeaderFontSize":14,"tableBodyFontSize":12,"tableFontWeight":"normal","enableTableCarousel":false,"tableRowsPerPage":5,"tableCarouselInterval":3,"showTablePagination":false,"hyperlinkUrl":"","hyperlinkText":"点击跳转","hyperlinkTarget":"_self","hyperlinkFontSize":16,"hyperlinkFontColor":"#007bff","hyperlinkFontWeight":"normal","hyperlinkTextAlign":"left","hyperlinkFontStyle":"normal","hyperlinkTextDecoration":"none","hyperlinkOpacity":100,"htmlOpacity":100,"videoUrl":"","poster":"","autoplay":false,"loop":false,"muted":true,"controls":true,"opacity":100,"borderWidth":0,"shadow":false,"timeFormat":"YYYY-MM-DD HH:mm:ss","timeFontSize":24,"timeFontWeight":"normal","timeFontStyle":"normal","timeColorType":"solid","timeColor":"#333333","timeGradientStart":"#667eea","timeGradientEnd":"#764ba2","timeGradientDirection":"to right","timeOpacity":100,"timeTextShadow":false}
bi-dashboard-designer.js?v=20250731-export-debug:11767 解析后的样式配置: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250731-export-debug:11768 配置项数量: 169
bi-dashboard-designer.js?v=20250731-export-debug:11769 重要配置检查: {showDataLabels: false, primaryColor: undefined, theme: 'default'}
bi-dashboard-designer.js?v=20250731-export-debug:11782 附加信息: {formConfigKeys: Array(169), hasFormConfig: true}
bi-dashboard-designer.js?v=20250731-export-debug:11784 === 追踪结束 ===
bi-dashboard-designer.js?v=20250731-export-debug:4793 表格组件配置更新，重新加载数据和样式
bi-dashboard-designer.js?v=20250731-export-debug:10030 更新组件数据: 3440 data-table
bi-data-source-manager.js?v=20250127:913 === BiDataSourceManager - 开始获取组件数据 ===
bi-data-source-manager.js?v=20250127:914 组件信息: {id: 3440, type: 'data-table', dataSourceConfig: '{"dataSourceType":"externalData","multiDataSet":tr…nTargetDataItem":"","showColumnTargetLabel":true}'}
bi-data-source-manager.js?v=20250127:959 解析字符串格式数据源配置: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(0), refreshInterval: 5, mergeStrategy: 'union', …}
bi-data-source-manager.js?v=20250127:974 确定的数据源类型: externalData
bi-data-source-manager.js?v=20250127:975 完整数据源配置: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(0), refreshInterval: 5, mergeStrategy: 'union', …}
bi-data-source-manager.js?v=20250127:990 处理外部数据源...
bi-data-source-manager.js?v=20250127:2198 处理多外部数据源数据: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(0), refreshInterval: 5, mergeStrategy: 'union', …}
bi-data-source-manager.js?v=20250127:2360  处理多外部数据源失败: Error: 未配置数据集
    at BiDataSourceManager.processMultiExternalData (bi-data-source-manager.js?v=20250127:2295:23)
    at BiDataSourceManager.processExternalData (bi-data-source-manager.js?v=20250127:2199:35)
    at BiDataSourceManager.fetchWidgetData (bi-data-source-manager.js?v=20250127:991:41)
    at updateWidgetData (bi-dashboard-designer.js?v=20250731-export-debug:10035:46)
    at applyPropertiesRealTime (bi-dashboard-designer.js?v=20250731-export-debug:4811:17)
    at onDataSourceTypeChange (bi-dashboard-designer.js?v=20250731-export-debug:6778:5)
    at HTMLSelectElement.onchange (design:2317:136)
processMultiExternalData @ bi-data-source-manager.js?v=20250127:2360
processExternalData @ bi-data-source-manager.js?v=20250127:2199
fetchWidgetData @ bi-data-source-manager.js?v=20250127:991
updateWidgetData @ bi-dashboard-designer.js?v=20250731-export-debug:10035
applyPropertiesRealTime @ bi-dashboard-designer.js?v=20250731-export-debug:4811
onDataSourceTypeChange @ bi-dashboard-designer.js?v=20250731-export-debug:6778
onchange @ design:2317
handleMouseUp_ @ 未知
bi-dashboard-designer.js?v=20250731-export-debug:12752 组件 3440 自动刷新条件检查: {dataSourceType: 'externalData', hasDataItemId: false, hasDataSetId: false, isMultiDataSet: true, dataSetsCount: 0, …}
bi-dashboard-designer.js?v=20250731-export-debug:12826 组件 3440 不满足自动刷新条件，但仍尝试初始数据加载
bi-dashboard-designer.js?v=20250731-export-debug:12834 组件 3440 检测到数据源配置，执行初始数据加载
bi-dashboard-designer.js?v=20250731-export-debug:4836 实时应用属性: {id: 3440, type: 'data-table', x: 116, y: 318, width: 300, …}
bi-data-source-manager.js?v=20250127:1000 数据获取结果: {success: false, message: '未配置数据集', labels: Array(1), values: Array(1), data: Array(1)}
bi-data-source-manager.js?v=20250127:1001 === BiDataSourceManager - 数据获取完成 ===
bi-dashboard-designer.js?v=20250731-export-debug:10044  数据获取失败: undefined
updateWidgetData @ bi-dashboard-designer.js?v=20250731-export-debug:10044
handleMouseUp_ @ 未知
await in handleMouseUp_
applyPropertiesRealTime @ bi-dashboard-designer.js?v=20250731-export-debug:4811
onDataSourceTypeChange @ bi-dashboard-designer.js?v=20250731-export-debug:6778
onchange @ design:2317
handleMouseUp_ @ 未知
bi-dashboard-designer.js?v=20250731-export-debug:14503 数据集列表加载成功，共 5 个数据集
bi-dashboard-designer.js?v=20250731-export-debug:14503 数据集列表加载成功，共 5 个数据集
bi-dashboard-designer.js?v=20250731-export-debug:12860 安全更新组件数据: 3440 data-table
bi-dashboard-designer.js?v=20250731-export-debug:10030 更新组件数据: 3440 data-table
bi-data-source-manager.js?v=20250127:913 === BiDataSourceManager - 开始获取组件数据 ===
bi-data-source-manager.js?v=20250127:914 组件信息: {id: 3440, type: 'data-table', dataSourceConfig: '{"dataSourceType":"externalData","multiDataSet":tr…nTargetDataItem":"","showColumnTargetLabel":true}'}
bi-data-source-manager.js?v=20250127:959 解析字符串格式数据源配置: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(0), refreshInterval: 5, mergeStrategy: 'union', …}
bi-data-source-manager.js?v=20250127:974 确定的数据源类型: externalData
bi-data-source-manager.js?v=20250127:975 完整数据源配置: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(0), refreshInterval: 5, mergeStrategy: 'union', …}
bi-data-source-manager.js?v=20250127:990 处理外部数据源...
bi-data-source-manager.js?v=20250127:2198 处理多外部数据源数据: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(0), refreshInterval: 5, mergeStrategy: 'union', …}
bi-data-source-manager.js?v=20250127:2360  处理多外部数据源失败: Error: 未配置数据集
    at BiDataSourceManager.processMultiExternalData (bi-data-source-manager.js?v=20250127:2295:23)
    at BiDataSourceManager.processExternalData (bi-data-source-manager.js?v=20250127:2199:35)
    at BiDataSourceManager.fetchWidgetData (bi-data-source-manager.js?v=20250127:991:41)
    at updateWidgetData (bi-dashboard-designer.js?v=20250731-export-debug:10035:46)
    at updateWidgetDataSafe (bi-dashboard-designer.js?v=20250731-export-debug:12871:15)
    at waitForChartInitFallback (bi-dashboard-designer.js?v=20250731-export-debug:12849:17)
processMultiExternalData @ bi-data-source-manager.js?v=20250127:2360
processExternalData @ bi-data-source-manager.js?v=20250127:2199
fetchWidgetData @ bi-data-source-manager.js?v=20250127:991
updateWidgetData @ bi-dashboard-designer.js?v=20250731-export-debug:10035
updateWidgetDataSafe @ bi-dashboard-designer.js?v=20250731-export-debug:12871
waitForChartInitFallback @ bi-dashboard-designer.js?v=20250731-export-debug:12849
setTimeout
setupWidgetAutoRefresh @ bi-dashboard-designer.js?v=20250731-export-debug:12853
applyPropertiesRealTime @ bi-dashboard-designer.js?v=20250731-export-debug:4824
onDataSourceTypeChange @ bi-dashboard-designer.js?v=20250731-export-debug:6778
onchange @ design:2317
handleMouseUp_ @ 未知
bi-dashboard-designer.js?v=20250731-export-debug:12850 组件 3440 兜底数据加载完成
bi-data-source-manager.js?v=20250127:1000 数据获取结果: {success: false, message: '未配置数据集', labels: Array(1), values: Array(1), data: Array(1)}
bi-data-source-manager.js?v=20250127:1001 === BiDataSourceManager - 数据获取完成 ===
bi-dashboard-designer.js?v=20250731-export-debug:10044  数据获取失败: undefined
updateWidgetData @ bi-dashboard-designer.js?v=20250731-export-debug:10044
await in updateWidgetData
updateWidgetDataSafe @ bi-dashboard-designer.js?v=20250731-export-debug:12871
waitForChartInitFallback @ bi-dashboard-designer.js?v=20250731-export-debug:12849
setTimeout
setupWidgetAutoRefresh @ bi-dashboard-designer.js?v=20250731-export-debug:12853
applyPropertiesRealTime @ bi-dashboard-designer.js?v=20250731-export-debug:4824
onDataSourceTypeChange @ bi-dashboard-designer.js?v=20250731-export-debug:6778
onchange @ design:2317
handleMouseUp_ @ 未知
bi-dashboard-designer.js?v=20250731-export-debug:464 组件 3440 (data-table) 已标记为修改: properties
bi-dashboard-designer.js?v=20250731-export-debug:4042 缓存组件 3440 配置，时间戳: 2025-08-01T06:09:01.213Z
bi-dashboard-designer.js?v=20250731-export-debug:532 同步组件 3440 配置缓存
bi-data-source-manager.js?v=20250127:207 已清理组件 3440 的配置上下文
bi-data-source-manager.js?v=20250127:179 为组件 3440 (data-table) 创建配置上下文
bi-data-source-manager.js?v=20250127:249 为组件 3440 缓存DOM元素: dataSourceType
bi-data-source-manager.js?v=20250127:1451 收集组件 3440 (data-table) 外部数据源配置，多数据集模式: false
bi-data-source-manager.js?v=20250127:1595 collectTableFieldConfig: 开始收集字段配置
bi-data-source-manager.js?v=20250127:1599 collectTableFieldConfig: 找到字段卡片数量: 0
bi-data-source-manager.js?v=20250127:1625 collectTableFieldConfig: 最终收集到的字段配置: []
bi-data-source-manager.js?v=20250127:1461 组件 3440 收集到单数据集配置，数据集ID: 
bi-data-source-manager.js?v=20250127:415  组件 3440 数据源配置验证失败: ['外部数据源缺少数据集配置']
collectDataSourceConfig @ bi-data-source-manager.js?v=20250127:415
applyPropertiesRealTime @ bi-dashboard-designer.js?v=20250731-export-debug:4510
onMultiExternalDataSetToggle @ bi-dashboard-designer.js?v=20250731-export-debug:15303
onchange @ design:2478
bi-data-source-manager.js?v=20250127:429 组件 3440 数据源配置收集完成: {dataSourceType: 'externalData', dataSetId: '', dataSetName: '请选择数据集', refreshInterval: 5, tableFields: Array(0), …}
bi-dashboard-designer.js?v=20250731-export-debug:11789 === 开始收集样式配置 ===
bi-dashboard-designer.js?v=20250731-export-debug:11405 🔧 [修复版本 v2.0] 开始收集多折线样式配置
bi-dashboard-designer.js?v=20250731-export-debug:11412 🔧 [修复版本] 折线数量: 1 (来源: 实际数据 )
bi-dashboard-designer.js?v=20250731-export-debug:11423 收集折线 1 的样式配置
bi-dashboard-designer.js?v=20250731-export-debug:11457 收集到的多折线样式配置: [{…}]
bi-dashboard-designer.js?v=20250731-export-debug:12484 === 样式配置收集完成 ===
bi-dashboard-designer.js?v=20250731-export-debug:12485 收集到的配置项数量: 169
bi-dashboard-designer.js?v=20250731-export-debug:12486 重要配置项检查: {showDataLabels: false, primaryColor: undefined, theme: 'default', showChartTitle: true, showLegend: true, …}
bi-dashboard-designer.js?v=20250731-export-debug:12502 完整样式配置: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250731-export-debug:4670 === 实时应用属性：样式配置更新 ===
bi-dashboard-designer.js?v=20250731-export-debug:4671 组件ID: 3440
bi-dashboard-designer.js?v=20250731-export-debug:4672 组件类型: data-table
bi-dashboard-designer.js?v=20250731-export-debug:4673 更新前styleConfig: {"theme":"default","backgroundColor":"#ffffff","borderColor":"#dee2e6","showTitle":true,"transparent":false,"titleFontSize":16,"showLegend":true,"showChartTitle":true,"borderlessMode":false,"showDataLabels":false,"showYAxis":true,"showYAxisLabels":true,"showXAxis":true,"yAxisFontSize":12,"yAxisFontColor":"#8c8c8c","xAxisFontSize":12,"xAxisFontColor":"#8c8c8c","dataLabelFontSize":11,"dataLabelFontColor":"#5470c6","legendFontSize":12,"legendFontColor":"#666666","lineWidth":3,"lineType":"solid","lineColor":"#5470c6","useGradientLine":false,"lineGradientColor":"#91cc75","smoothLine":true,"showArea":false,"useGradientArea":true,"areaStartColor":"#5470c6","areaMidColor":"#91cc75","areaEndColor":"#ffffff","showSymbol":true,"symbolType":"circle","symbolSize":8,"symbolColor":"#5470c6","enableShadow":false,"shadowBlur":10,"shadowOffsetY":3,"shadowColor":"#000000","multiLineSmooth":true,"multiLineShowSymbol":true,"multiLineSymbolSize":6,"multiLineWidth":2,"multiLineShowArea":false,"multiLineEnableDualYAxis":false,"useIndividualStyles":true,"lineCount":1,"individualLineStyles":[{"color":"#5470c6","width":2,"type":"solid","useGradient":false,"gradientColor":"#91cc75","showSymbol":true,"symbolSize":6,"symbolType":"circle","symbolColor":"#5470c6","symbolBorderColor":"#ffffff","symbolBorderWidth":0,"showArea":false,"areaOpacity":30,"showLabel":false,"labelPosition":"top","labelColor":"#333333","labelFontSize":12,"labelFontWeight":"normal"}],"barWidth":60,"borderRadius":0,"borderRadiusMode":"top","barColor":"#5470c6","useGradientBar":false,"barStartColor":"#5470c6","barEndColor":"#91cc75","gaugeMin":0,"gaugeMax":100,"gaugeUnit":"%","gaugeRadius":80,"pieType":"ring","pieRadius":70,"pieInnerRadius":40,"pieOuterRadius":70,"pieBorderRadius":0,"pieBorderRadiusMode":"all","showPieBorder":true,"pieShowDataLabels":true,"useCustomPieColors":false,"waterBorderWidth":5,"waterBorderColor":"#1890ff","useWaterBorderGradient":false,"waterBorderGradientColor":"#52c41a","waterWaveColor":"#1890ff","useWaterWaveGradient":false,"waterWaveGradientColor":"#52c41a","waterAmplitude":20,"showWaterLabel":true,"waterLabelFontSize":20,"waterLabelFontColor":"#333333","showPercentageSymbol":true,"columnBarWidth":40,"columnBorderRadius":8,"columnUseGradient":true,"columnStartColor":"#4facfe","columnEndColor":"#00f2fe","columnEnableShadow":true,"columnShadowColor":"#000000","columnShadowBlur":10,"columnShowDataLabel":true,"columnDataLabelPosition":"vertical-top","columnDataLabelColor":"#333333","columnDataLabelFontSize":14,"columnShowPercentage":true,"columnShowBarBackground":true,"columnBarBackgroundColor":"#f5f5f5","columnAllowOverflow":true,"textFontFamily":"Arial, sans-serif","textFontSize":16,"textFontColor":"#333333","textFontWeight":"normal","textFontStyle":"normal","textDecoration":"none","textAlign":"left","textVerticalAlign":"top","textLineHeight":1.5,"textLetterSpacing":0,"textPadding":10,"textBorderRadius":0,"enableTextGradient":false,"textGradientType":"linear","textGradientStartColor":"#ffffff","textGradientEndColor":"#f0f0f0","textGradientAngle":0,"imageUrl":"","imageBorderRadius":0,"imageRotation":0,"imageOpacity":100,"imageObjectFit":"contain","decorationObjectFit":"contain","decorationOpacity":100,"decorationRotation":0,"decorationBlendMode":"normal","tableStyle":"table","tableSize":"table-normal","showTableHeader":true,"tableHeaderBgColor":"#f8f9fa","tableHeaderTextColor":"#212529","tableBodyBgColor":"#ffffff","tableBodyTextColor":"#212529","tableBorderColor":"#dee2e6","tableHeaderFontSize":14,"tableBodyFontSize":12,"tableFontWeight":"normal","enableTableCarousel":false,"tableRowsPerPage":5,"tableCarouselInterval":3,"showTablePagination":false,"hyperlinkUrl":"","hyperlinkText":"点击跳转","hyperlinkTarget":"_self","hyperlinkFontSize":16,"hyperlinkFontColor":"#007bff","hyperlinkFontWeight":"normal","hyperlinkTextAlign":"left","hyperlinkFontStyle":"normal","hyperlinkTextDecoration":"none","hyperlinkOpacity":100,"htmlOpacity":100,"videoUrl":"","poster":"","autoplay":false,"loop":false,"muted":true,"controls":true,"opacity":100,"borderWidth":0,"shadow":false,"timeFormat":"YYYY-MM-DD HH:mm:ss","timeFontSize":24,"timeFontWeight":"normal","timeFontStyle":"normal","timeColorType":"solid","timeColor":"#333333","timeGradientStart":"#667eea","timeGradientEnd":"#764ba2","timeGradientDirection":"to right","timeOpacity":100,"timeTextShadow":false}
bi-dashboard-designer.js?v=20250731-export-debug:4674 新的styleConfig: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250731-export-debug:4683 更新后styleConfig字符串: {"theme":"default","backgroundColor":"#ffffff","borderColor":"#dee2e6","showTitle":true,"transparent":false,"titleFontSize":16,"showLegend":true,"showChartTitle":true,"borderlessMode":false,"showDataLabels":false,"showYAxis":true,"showYAxisLabels":true,"showXAxis":true,"yAxisFontSize":12,"yAxisFontColor":"#8c8c8c","xAxisFontSize":12,"xAxisFontColor":"#8c8c8c","dataLabelFontSize":11,"dataLabelFontColor":"#5470c6","legendFontSize":12,"legendFontColor":"#666666","lineWidth":3,"lineType":"solid","lineColor":"#5470c6","useGradientLine":false,"lineGradientColor":"#91cc75","smoothLine":true,"showArea":false,"useGradientArea":true,"areaStartColor":"#5470c6","areaMidColor":"#91cc75","areaEndColor":"#ffffff","showSymbol":true,"symbolType":"circle","symbolSize":8,"symbolColor":"#5470c6","enableShadow":false,"shadowBlur":10,"shadowOffsetY":3,"shadowColor":"#000000","multiLineSmooth":true,"multiLineShowSymbol":true,"multiLineSymbolSize":6,"multiLineWidth":2,"multiLineShowArea":false,"multiLineEnableDualYAxis":false,"useIndividualStyles":true,"lineCount":1,"individualLineStyles":[{"color":"#5470c6","width":2,"type":"solid","useGradient":false,"gradientColor":"#91cc75","showSymbol":true,"symbolSize":6,"symbolType":"circle","symbolColor":"#5470c6","symbolBorderColor":"#ffffff","symbolBorderWidth":0,"showArea":false,"areaOpacity":30,"showLabel":false,"labelPosition":"top","labelColor":"#333333","labelFontSize":12,"labelFontWeight":"normal"}],"barWidth":60,"borderRadius":0,"borderRadiusMode":"top","barColor":"#5470c6","useGradientBar":false,"barStartColor":"#5470c6","barEndColor":"#91cc75","gaugeMin":0,"gaugeMax":100,"gaugeUnit":"%","gaugeRadius":80,"pieType":"ring","pieRadius":70,"pieInnerRadius":40,"pieOuterRadius":70,"pieBorderRadius":0,"pieBorderRadiusMode":"all","showPieBorder":true,"pieShowDataLabels":true,"useCustomPieColors":false,"waterBorderWidth":5,"waterBorderColor":"#1890ff","useWaterBorderGradient":false,"waterBorderGradientColor":"#52c41a","waterWaveColor":"#1890ff","useWaterWaveGradient":false,"waterWaveGradientColor":"#52c41a","waterAmplitude":20,"showWaterLabel":true,"waterLabelFontSize":20,"waterLabelFontColor":"#333333","showPercentageSymbol":true,"columnBarWidth":40,"columnBorderRadius":8,"columnUseGradient":true,"columnStartColor":"#4facfe","columnEndColor":"#00f2fe","columnEnableShadow":true,"columnShadowColor":"#000000","columnShadowBlur":10,"columnShowDataLabel":true,"columnDataLabelPosition":"vertical-top","columnDataLabelColor":"#333333","columnDataLabelFontSize":14,"columnShowPercentage":true,"columnShowBarBackground":true,"columnBarBackgroundColor":"#f5f5f5","columnAllowOverflow":true,"textFontFamily":"Arial, sans-serif","textFontSize":16,"textFontColor":"#333333","textFontWeight":"normal","textFontStyle":"normal","textDecoration":"none","textAlign":"left","textVerticalAlign":"top","textLineHeight":1.5,"textLetterSpacing":0,"textPadding":10,"textBorderRadius":0,"enableTextGradient":false,"textGradientType":"linear","textGradientStartColor":"#ffffff","textGradientEndColor":"#f0f0f0","textGradientAngle":0,"imageUrl":"","imageBorderRadius":0,"imageRotation":0,"imageOpacity":100,"imageObjectFit":"contain","decorationObjectFit":"contain","decorationOpacity":100,"decorationRotation":0,"decorationBlendMode":"normal","tableStyle":"table","tableSize":"table-normal","showTableHeader":true,"tableHeaderBgColor":"#f8f9fa","tableHeaderTextColor":"#212529","tableBodyBgColor":"#ffffff","tableBodyTextColor":"#212529","tableBorderColor":"#dee2e6","tableHeaderFontSize":14,"tableBodyFontSize":12,"tableFontWeight":"normal","enableTableCarousel":false,"tableRowsPerPage":5,"tableCarouselInterval":3,"showTablePagination":false,"hyperlinkUrl":"","hyperlinkText":"点击跳转","hyperlinkTarget":"_self","hyperlinkFontSize":16,"hyperlinkFontColor":"#007bff","hyperlinkFontWeight":"normal","hyperlinkTextAlign":"left","hyperlinkFontStyle":"normal","hyperlinkTextDecoration":"none","hyperlinkOpacity":100,"htmlOpacity":100,"videoUrl":"","poster":"","autoplay":false,"loop":false,"muted":true,"controls":true,"opacity":100,"borderWidth":0,"shadow":false,"timeFormat":"YYYY-MM-DD HH:mm:ss","timeFontSize":24,"timeFontWeight":"normal","timeFontStyle":"normal","timeColorType":"solid","timeColor":"#333333","timeGradientStart":"#667eea","timeGradientEnd":"#764ba2","timeGradientDirection":"to right","timeOpacity":100,"timeTextShadow":false}
bi-dashboard-designer.js?v=20250731-export-debug:4684 === 样式配置更新完成 ===
bi-dashboard-designer.js?v=20250731-export-debug:11759 === 样式配置生命周期追踪：实时应用后 ===
bi-dashboard-designer.js?v=20250731-export-debug:11760 组件ID: 3440
bi-dashboard-designer.js?v=20250731-export-debug:11761 组件类型: data-table
bi-dashboard-designer.js?v=20250731-export-debug:11762 当前styleConfig: {"theme":"default","backgroundColor":"#ffffff","borderColor":"#dee2e6","showTitle":true,"transparent":false,"titleFontSize":16,"showLegend":true,"showChartTitle":true,"borderlessMode":false,"showDataLabels":false,"showYAxis":true,"showYAxisLabels":true,"showXAxis":true,"yAxisFontSize":12,"yAxisFontColor":"#8c8c8c","xAxisFontSize":12,"xAxisFontColor":"#8c8c8c","dataLabelFontSize":11,"dataLabelFontColor":"#5470c6","legendFontSize":12,"legendFontColor":"#666666","lineWidth":3,"lineType":"solid","lineColor":"#5470c6","useGradientLine":false,"lineGradientColor":"#91cc75","smoothLine":true,"showArea":false,"useGradientArea":true,"areaStartColor":"#5470c6","areaMidColor":"#91cc75","areaEndColor":"#ffffff","showSymbol":true,"symbolType":"circle","symbolSize":8,"symbolColor":"#5470c6","enableShadow":false,"shadowBlur":10,"shadowOffsetY":3,"shadowColor":"#000000","multiLineSmooth":true,"multiLineShowSymbol":true,"multiLineSymbolSize":6,"multiLineWidth":2,"multiLineShowArea":false,"multiLineEnableDualYAxis":false,"useIndividualStyles":true,"lineCount":1,"individualLineStyles":[{"color":"#5470c6","width":2,"type":"solid","useGradient":false,"gradientColor":"#91cc75","showSymbol":true,"symbolSize":6,"symbolType":"circle","symbolColor":"#5470c6","symbolBorderColor":"#ffffff","symbolBorderWidth":0,"showArea":false,"areaOpacity":30,"showLabel":false,"labelPosition":"top","labelColor":"#333333","labelFontSize":12,"labelFontWeight":"normal"}],"barWidth":60,"borderRadius":0,"borderRadiusMode":"top","barColor":"#5470c6","useGradientBar":false,"barStartColor":"#5470c6","barEndColor":"#91cc75","gaugeMin":0,"gaugeMax":100,"gaugeUnit":"%","gaugeRadius":80,"pieType":"ring","pieRadius":70,"pieInnerRadius":40,"pieOuterRadius":70,"pieBorderRadius":0,"pieBorderRadiusMode":"all","showPieBorder":true,"pieShowDataLabels":true,"useCustomPieColors":false,"waterBorderWidth":5,"waterBorderColor":"#1890ff","useWaterBorderGradient":false,"waterBorderGradientColor":"#52c41a","waterWaveColor":"#1890ff","useWaterWaveGradient":false,"waterWaveGradientColor":"#52c41a","waterAmplitude":20,"showWaterLabel":true,"waterLabelFontSize":20,"waterLabelFontColor":"#333333","showPercentageSymbol":true,"columnBarWidth":40,"columnBorderRadius":8,"columnUseGradient":true,"columnStartColor":"#4facfe","columnEndColor":"#00f2fe","columnEnableShadow":true,"columnShadowColor":"#000000","columnShadowBlur":10,"columnShowDataLabel":true,"columnDataLabelPosition":"vertical-top","columnDataLabelColor":"#333333","columnDataLabelFontSize":14,"columnShowPercentage":true,"columnShowBarBackground":true,"columnBarBackgroundColor":"#f5f5f5","columnAllowOverflow":true,"textFontFamily":"Arial, sans-serif","textFontSize":16,"textFontColor":"#333333","textFontWeight":"normal","textFontStyle":"normal","textDecoration":"none","textAlign":"left","textVerticalAlign":"top","textLineHeight":1.5,"textLetterSpacing":0,"textPadding":10,"textBorderRadius":0,"enableTextGradient":false,"textGradientType":"linear","textGradientStartColor":"#ffffff","textGradientEndColor":"#f0f0f0","textGradientAngle":0,"imageUrl":"","imageBorderRadius":0,"imageRotation":0,"imageOpacity":100,"imageObjectFit":"contain","decorationObjectFit":"contain","decorationOpacity":100,"decorationRotation":0,"decorationBlendMode":"normal","tableStyle":"table","tableSize":"table-normal","showTableHeader":true,"tableHeaderBgColor":"#f8f9fa","tableHeaderTextColor":"#212529","tableBodyBgColor":"#ffffff","tableBodyTextColor":"#212529","tableBorderColor":"#dee2e6","tableHeaderFontSize":14,"tableBodyFontSize":12,"tableFontWeight":"normal","enableTableCarousel":false,"tableRowsPerPage":5,"tableCarouselInterval":3,"showTablePagination":false,"hyperlinkUrl":"","hyperlinkText":"点击跳转","hyperlinkTarget":"_self","hyperlinkFontSize":16,"hyperlinkFontColor":"#007bff","hyperlinkFontWeight":"normal","hyperlinkTextAlign":"left","hyperlinkFontStyle":"normal","hyperlinkTextDecoration":"none","hyperlinkOpacity":100,"htmlOpacity":100,"videoUrl":"","poster":"","autoplay":false,"loop":false,"muted":true,"controls":true,"opacity":100,"borderWidth":0,"shadow":false,"timeFormat":"YYYY-MM-DD HH:mm:ss","timeFontSize":24,"timeFontWeight":"normal","timeFontStyle":"normal","timeColorType":"solid","timeColor":"#333333","timeGradientStart":"#667eea","timeGradientEnd":"#764ba2","timeGradientDirection":"to right","timeOpacity":100,"timeTextShadow":false}
bi-dashboard-designer.js?v=20250731-export-debug:11767 解析后的样式配置: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250731-export-debug:11768 配置项数量: 169
bi-dashboard-designer.js?v=20250731-export-debug:11769 重要配置检查: {showDataLabels: false, primaryColor: undefined, theme: 'default'}
bi-dashboard-designer.js?v=20250731-export-debug:11782 附加信息: {formConfigKeys: Array(169), hasFormConfig: true}
bi-dashboard-designer.js?v=20250731-export-debug:11784 === 追踪结束 ===
bi-dashboard-designer.js?v=20250731-export-debug:4793 表格组件配置更新，重新加载数据和样式
bi-dashboard-designer.js?v=20250731-export-debug:10030 更新组件数据: 3440 data-table
bi-data-source-manager.js?v=20250127:913 === BiDataSourceManager - 开始获取组件数据 ===
bi-data-source-manager.js?v=20250127:914 组件信息: {id: 3440, type: 'data-table', dataSourceConfig: '{"dataSourceType":"externalData","dataSetId":"","d…nTargetDataItem":"","showColumnTargetLabel":true}'}
bi-data-source-manager.js?v=20250127:959 解析字符串格式数据源配置: {dataSourceType: 'externalData', dataSetId: '', dataSetName: '请选择数据集', refreshInterval: 5, tableFields: Array(0), …}
bi-data-source-manager.js?v=20250127:974 确定的数据源类型: externalData
bi-data-source-manager.js?v=20250127:975 完整数据源配置: {dataSourceType: 'externalData', dataSetId: '', dataSetName: '请选择数据集', refreshInterval: 5, tableFields: Array(0), …}
bi-data-source-manager.js?v=20250127:990 处理外部数据源...
bi-data-source-manager.js?v=20250127:2283  处理外部数据源失败: Error: 未选择数据集
    at BiDataSourceManager.processExternalData (bi-data-source-manager.js?v=20250127:2204:23)
    at BiDataSourceManager.fetchWidgetData (bi-data-source-manager.js?v=20250127:991:41)
    at updateWidgetData (bi-dashboard-designer.js?v=20250731-export-debug:10035:46)
    at applyPropertiesRealTime (bi-dashboard-designer.js?v=20250731-export-debug:4811:17)
    at onMultiExternalDataSetToggle (bi-dashboard-designer.js?v=20250731-export-debug:15303:5)
    at HTMLInputElement.onchange (design:2478:168)
processExternalData @ bi-data-source-manager.js?v=20250127:2283
fetchWidgetData @ bi-data-source-manager.js?v=20250127:991
updateWidgetData @ bi-dashboard-designer.js?v=20250731-export-debug:10035
applyPropertiesRealTime @ bi-dashboard-designer.js?v=20250731-export-debug:4811
onMultiExternalDataSetToggle @ bi-dashboard-designer.js?v=20250731-export-debug:15303
onchange @ design:2478
bi-dashboard-designer.js?v=20250731-export-debug:12752 组件 3440 自动刷新条件检查: {dataSourceType: 'externalData', hasDataItemId: false, hasDataSetId: false, isMultiDataSet: false, dataSetsCount: 0, …}
bi-dashboard-designer.js?v=20250731-export-debug:12826 组件 3440 不满足自动刷新条件，但仍尝试初始数据加载
bi-dashboard-designer.js?v=20250731-export-debug:12834 组件 3440 检测到数据源配置，执行初始数据加载
bi-dashboard-designer.js?v=20250731-export-debug:4836 实时应用属性: {id: 3440, type: 'data-table', x: 116, y: 318, width: 300, …}
bi-data-source-manager.js?v=20250127:1000 数据获取结果: {success: false, message: '未选择数据集', labels: Array(1), values: Array(1), data: Array(1)}
bi-data-source-manager.js?v=20250127:1001 === BiDataSourceManager - 数据获取完成 ===
bi-dashboard-designer.js?v=20250731-export-debug:10044  数据获取失败: undefined
updateWidgetData @ bi-dashboard-designer.js?v=20250731-export-debug:10044
await in updateWidgetData
applyPropertiesRealTime @ bi-dashboard-designer.js?v=20250731-export-debug:4811
onMultiExternalDataSetToggle @ bi-dashboard-designer.js?v=20250731-export-debug:15303
onchange @ design:2478
bi-dashboard-designer.js?v=20250731-export-debug:12860 安全更新组件数据: 3440 data-table
bi-dashboard-designer.js?v=20250731-export-debug:10030 更新组件数据: 3440 data-table
bi-data-source-manager.js?v=20250127:913 === BiDataSourceManager - 开始获取组件数据 ===
bi-data-source-manager.js?v=20250127:914 组件信息: {id: 3440, type: 'data-table', dataSourceConfig: '{"dataSourceType":"externalData","dataSetId":"","d…nTargetDataItem":"","showColumnTargetLabel":true}'}
bi-data-source-manager.js?v=20250127:959 解析字符串格式数据源配置: {dataSourceType: 'externalData', dataSetId: '', dataSetName: '请选择数据集', refreshInterval: 5, tableFields: Array(0), …}
bi-data-source-manager.js?v=20250127:974 确定的数据源类型: externalData
bi-data-source-manager.js?v=20250127:975 完整数据源配置: {dataSourceType: 'externalData', dataSetId: '', dataSetName: '请选择数据集', refreshInterval: 5, tableFields: Array(0), …}
bi-data-source-manager.js?v=20250127:990 处理外部数据源...
bi-data-source-manager.js?v=20250127:2283  处理外部数据源失败: Error: 未选择数据集
    at BiDataSourceManager.processExternalData (bi-data-source-manager.js?v=20250127:2204:23)
    at BiDataSourceManager.fetchWidgetData (bi-data-source-manager.js?v=20250127:991:41)
    at updateWidgetData (bi-dashboard-designer.js?v=20250731-export-debug:10035:46)
    at updateWidgetDataSafe (bi-dashboard-designer.js?v=20250731-export-debug:12871:15)
    at waitForChartInitFallback (bi-dashboard-designer.js?v=20250731-export-debug:12849:17)
processExternalData @ bi-data-source-manager.js?v=20250127:2283
fetchWidgetData @ bi-data-source-manager.js?v=20250127:991
updateWidgetData @ bi-dashboard-designer.js?v=20250731-export-debug:10035
updateWidgetDataSafe @ bi-dashboard-designer.js?v=20250731-export-debug:12871
waitForChartInitFallback @ bi-dashboard-designer.js?v=20250731-export-debug:12849
setTimeout
setupWidgetAutoRefresh @ bi-dashboard-designer.js?v=20250731-export-debug:12853
applyPropertiesRealTime @ bi-dashboard-designer.js?v=20250731-export-debug:4824
onMultiExternalDataSetToggle @ bi-dashboard-designer.js?v=20250731-export-debug:15303
onchange @ design:2478
bi-dashboard-designer.js?v=20250731-export-debug:12850 组件 3440 兜底数据加载完成
bi-data-source-manager.js?v=20250127:1000 数据获取结果: {success: false, message: '未选择数据集', labels: Array(1), values: Array(1), data: Array(1)}
bi-data-source-manager.js?v=20250127:1001 === BiDataSourceManager - 数据获取完成 ===
bi-dashboard-designer.js?v=20250731-export-debug:10044  数据获取失败: undefined
updateWidgetData @ bi-dashboard-designer.js?v=20250731-export-debug:10044
await in updateWidgetData
updateWidgetDataSafe @ bi-dashboard-designer.js?v=20250731-export-debug:12871
waitForChartInitFallback @ bi-dashboard-designer.js?v=20250731-export-debug:12849
setTimeout
setupWidgetAutoRefresh @ bi-dashboard-designer.js?v=20250731-export-debug:12853
applyPropertiesRealTime @ bi-dashboard-designer.js?v=20250731-export-debug:4824
onMultiExternalDataSetToggle @ bi-dashboard-designer.js?v=20250731-export-debug:15303
onchange @ design:2478
bi-dashboard-designer.js?v=20250731-export-debug:15320 初始化组件 3440 (data-table) 的多数据集配置，容器ID: tableExternalDataSourceList
bi-dashboard-designer.js?v=20250731-export-debug:15330 清理容器 tableExternalDataSourceList 的内容
bi-dashboard-designer.js?v=20250731-export-debug:15494 在容器 tableExternalDataSourceList 中创建新的数据集项: externalDataSet_0
bi-dashboard-designer.js?v=20250731-export-debug:464 组件 3440 (data-table) 已标记为修改: properties
bi-dashboard-designer.js?v=20250731-export-debug:4042 缓存组件 3440 配置，时间戳: 2025-08-01T06:09:01.597Z
bi-dashboard-designer.js?v=20250731-export-debug:532 同步组件 3440 配置缓存
bi-data-source-manager.js?v=20250127:207 已清理组件 3440 的配置上下文
bi-data-source-manager.js?v=20250127:179 为组件 3440 (data-table) 创建配置上下文
bi-data-source-manager.js?v=20250127:249 为组件 3440 缓存DOM元素: dataSourceType
bi-data-source-manager.js?v=20250127:1451 收集组件 3440 (data-table) 外部数据源配置，多数据集模式: true
bi-data-source-manager.js?v=20250127:1513 收集多数据集配置 - 容器ID: tableExternalDataSourceList
bi-data-source-manager.js?v=20250127:1514 收集多数据集配置 - 找到数据集项数量: 1
bi-data-source-manager.js?v=20250127:1523 数据集项 DOM索引0: {itemId: 'externalDataSet_0', dataSetId: '', dataSetName: '加载中...', labelField: 'null', valueField: 'null', …}
bi-data-source-manager.js?v=20250127:1567 跳过数据集项 DOM索引0: 未选择数据集
bi-data-source-manager.js?v=20250127:1571 最终收集到的数据集配置: []
bi-data-source-manager.js?v=20250127:1456 组件 3440 收集到多数据集配置，数据集数量: 0
bi-data-source-manager.js?v=20250127:429 组件 3440 数据源配置收集完成: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(0), refreshInterval: 5, mergeStrategy: 'union'}
bi-dashboard-designer.js?v=20250731-export-debug:11789 === 开始收集样式配置 ===
bi-dashboard-designer.js?v=20250731-export-debug:11405 🔧 [修复版本 v2.0] 开始收集多折线样式配置
bi-dashboard-designer.js?v=20250731-export-debug:11412 🔧 [修复版本] 折线数量: 1 (来源: 实际数据 )
bi-dashboard-designer.js?v=20250731-export-debug:11423 收集折线 1 的样式配置
bi-dashboard-designer.js?v=20250731-export-debug:11457 收集到的多折线样式配置: [{…}]
bi-dashboard-designer.js?v=20250731-export-debug:12484 === 样式配置收集完成 ===
bi-dashboard-designer.js?v=20250731-export-debug:12485 收集到的配置项数量: 169
bi-dashboard-designer.js?v=20250731-export-debug:12486 重要配置项检查: {showDataLabels: false, primaryColor: undefined, theme: 'default', showChartTitle: true, showLegend: true, …}
bi-dashboard-designer.js?v=20250731-export-debug:12502 完整样式配置: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250731-export-debug:4670 === 实时应用属性：样式配置更新 ===
bi-dashboard-designer.js?v=20250731-export-debug:4671 组件ID: 3440
bi-dashboard-designer.js?v=20250731-export-debug:4672 组件类型: data-table
bi-dashboard-designer.js?v=20250731-export-debug:4673 更新前styleConfig: {"theme":"default","backgroundColor":"#ffffff","borderColor":"#dee2e6","showTitle":true,"transparent":false,"titleFontSize":16,"showLegend":true,"showChartTitle":true,"borderlessMode":false,"showDataLabels":false,"showYAxis":true,"showYAxisLabels":true,"showXAxis":true,"yAxisFontSize":12,"yAxisFontColor":"#8c8c8c","xAxisFontSize":12,"xAxisFontColor":"#8c8c8c","dataLabelFontSize":11,"dataLabelFontColor":"#5470c6","legendFontSize":12,"legendFontColor":"#666666","lineWidth":3,"lineType":"solid","lineColor":"#5470c6","useGradientLine":false,"lineGradientColor":"#91cc75","smoothLine":true,"showArea":false,"useGradientArea":true,"areaStartColor":"#5470c6","areaMidColor":"#91cc75","areaEndColor":"#ffffff","showSymbol":true,"symbolType":"circle","symbolSize":8,"symbolColor":"#5470c6","enableShadow":false,"shadowBlur":10,"shadowOffsetY":3,"shadowColor":"#000000","multiLineSmooth":true,"multiLineShowSymbol":true,"multiLineSymbolSize":6,"multiLineWidth":2,"multiLineShowArea":false,"multiLineEnableDualYAxis":false,"useIndividualStyles":true,"lineCount":1,"individualLineStyles":[{"color":"#5470c6","width":2,"type":"solid","useGradient":false,"gradientColor":"#91cc75","showSymbol":true,"symbolSize":6,"symbolType":"circle","symbolColor":"#5470c6","symbolBorderColor":"#ffffff","symbolBorderWidth":0,"showArea":false,"areaOpacity":30,"showLabel":false,"labelPosition":"top","labelColor":"#333333","labelFontSize":12,"labelFontWeight":"normal"}],"barWidth":60,"borderRadius":0,"borderRadiusMode":"top","barColor":"#5470c6","useGradientBar":false,"barStartColor":"#5470c6","barEndColor":"#91cc75","gaugeMin":0,"gaugeMax":100,"gaugeUnit":"%","gaugeRadius":80,"pieType":"ring","pieRadius":70,"pieInnerRadius":40,"pieOuterRadius":70,"pieBorderRadius":0,"pieBorderRadiusMode":"all","showPieBorder":true,"pieShowDataLabels":true,"useCustomPieColors":false,"waterBorderWidth":5,"waterBorderColor":"#1890ff","useWaterBorderGradient":false,"waterBorderGradientColor":"#52c41a","waterWaveColor":"#1890ff","useWaterWaveGradient":false,"waterWaveGradientColor":"#52c41a","waterAmplitude":20,"showWaterLabel":true,"waterLabelFontSize":20,"waterLabelFontColor":"#333333","showPercentageSymbol":true,"columnBarWidth":40,"columnBorderRadius":8,"columnUseGradient":true,"columnStartColor":"#4facfe","columnEndColor":"#00f2fe","columnEnableShadow":true,"columnShadowColor":"#000000","columnShadowBlur":10,"columnShowDataLabel":true,"columnDataLabelPosition":"vertical-top","columnDataLabelColor":"#333333","columnDataLabelFontSize":14,"columnShowPercentage":true,"columnShowBarBackground":true,"columnBarBackgroundColor":"#f5f5f5","columnAllowOverflow":true,"textFontFamily":"Arial, sans-serif","textFontSize":16,"textFontColor":"#333333","textFontWeight":"normal","textFontStyle":"normal","textDecoration":"none","textAlign":"left","textVerticalAlign":"top","textLineHeight":1.5,"textLetterSpacing":0,"textPadding":10,"textBorderRadius":0,"enableTextGradient":false,"textGradientType":"linear","textGradientStartColor":"#ffffff","textGradientEndColor":"#f0f0f0","textGradientAngle":0,"imageUrl":"","imageBorderRadius":0,"imageRotation":0,"imageOpacity":100,"imageObjectFit":"contain","decorationObjectFit":"contain","decorationOpacity":100,"decorationRotation":0,"decorationBlendMode":"normal","tableStyle":"table","tableSize":"table-normal","showTableHeader":true,"tableHeaderBgColor":"#f8f9fa","tableHeaderTextColor":"#212529","tableBodyBgColor":"#ffffff","tableBodyTextColor":"#212529","tableBorderColor":"#dee2e6","tableHeaderFontSize":14,"tableBodyFontSize":12,"tableFontWeight":"normal","enableTableCarousel":false,"tableRowsPerPage":5,"tableCarouselInterval":3,"showTablePagination":false,"hyperlinkUrl":"","hyperlinkText":"点击跳转","hyperlinkTarget":"_self","hyperlinkFontSize":16,"hyperlinkFontColor":"#007bff","hyperlinkFontWeight":"normal","hyperlinkTextAlign":"left","hyperlinkFontStyle":"normal","hyperlinkTextDecoration":"none","hyperlinkOpacity":100,"htmlOpacity":100,"videoUrl":"","poster":"","autoplay":false,"loop":false,"muted":true,"controls":true,"opacity":100,"borderWidth":0,"shadow":false,"timeFormat":"YYYY-MM-DD HH:mm:ss","timeFontSize":24,"timeFontWeight":"normal","timeFontStyle":"normal","timeColorType":"solid","timeColor":"#333333","timeGradientStart":"#667eea","timeGradientEnd":"#764ba2","timeGradientDirection":"to right","timeOpacity":100,"timeTextShadow":false}
bi-dashboard-designer.js?v=20250731-export-debug:4674 新的styleConfig: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250731-export-debug:4683 更新后styleConfig字符串: {"theme":"default","backgroundColor":"#ffffff","borderColor":"#dee2e6","showTitle":true,"transparent":false,"titleFontSize":16,"showLegend":true,"showChartTitle":true,"borderlessMode":false,"showDataLabels":false,"showYAxis":true,"showYAxisLabels":true,"showXAxis":true,"yAxisFontSize":12,"yAxisFontColor":"#8c8c8c","xAxisFontSize":12,"xAxisFontColor":"#8c8c8c","dataLabelFontSize":11,"dataLabelFontColor":"#5470c6","legendFontSize":12,"legendFontColor":"#666666","lineWidth":3,"lineType":"solid","lineColor":"#5470c6","useGradientLine":false,"lineGradientColor":"#91cc75","smoothLine":true,"showArea":false,"useGradientArea":true,"areaStartColor":"#5470c6","areaMidColor":"#91cc75","areaEndColor":"#ffffff","showSymbol":true,"symbolType":"circle","symbolSize":8,"symbolColor":"#5470c6","enableShadow":false,"shadowBlur":10,"shadowOffsetY":3,"shadowColor":"#000000","multiLineSmooth":true,"multiLineShowSymbol":true,"multiLineSymbolSize":6,"multiLineWidth":2,"multiLineShowArea":false,"multiLineEnableDualYAxis":false,"useIndividualStyles":true,"lineCount":1,"individualLineStyles":[{"color":"#5470c6","width":2,"type":"solid","useGradient":false,"gradientColor":"#91cc75","showSymbol":true,"symbolSize":6,"symbolType":"circle","symbolColor":"#5470c6","symbolBorderColor":"#ffffff","symbolBorderWidth":0,"showArea":false,"areaOpacity":30,"showLabel":false,"labelPosition":"top","labelColor":"#333333","labelFontSize":12,"labelFontWeight":"normal"}],"barWidth":60,"borderRadius":0,"borderRadiusMode":"top","barColor":"#5470c6","useGradientBar":false,"barStartColor":"#5470c6","barEndColor":"#91cc75","gaugeMin":0,"gaugeMax":100,"gaugeUnit":"%","gaugeRadius":80,"pieType":"ring","pieRadius":70,"pieInnerRadius":40,"pieOuterRadius":70,"pieBorderRadius":0,"pieBorderRadiusMode":"all","showPieBorder":true,"pieShowDataLabels":true,"useCustomPieColors":false,"waterBorderWidth":5,"waterBorderColor":"#1890ff","useWaterBorderGradient":false,"waterBorderGradientColor":"#52c41a","waterWaveColor":"#1890ff","useWaterWaveGradient":false,"waterWaveGradientColor":"#52c41a","waterAmplitude":20,"showWaterLabel":true,"waterLabelFontSize":20,"waterLabelFontColor":"#333333","showPercentageSymbol":true,"columnBarWidth":40,"columnBorderRadius":8,"columnUseGradient":true,"columnStartColor":"#4facfe","columnEndColor":"#00f2fe","columnEnableShadow":true,"columnShadowColor":"#000000","columnShadowBlur":10,"columnShowDataLabel":true,"columnDataLabelPosition":"vertical-top","columnDataLabelColor":"#333333","columnDataLabelFontSize":14,"columnShowPercentage":true,"columnShowBarBackground":true,"columnBarBackgroundColor":"#f5f5f5","columnAllowOverflow":true,"textFontFamily":"Arial, sans-serif","textFontSize":16,"textFontColor":"#333333","textFontWeight":"normal","textFontStyle":"normal","textDecoration":"none","textAlign":"left","textVerticalAlign":"top","textLineHeight":1.5,"textLetterSpacing":0,"textPadding":10,"textBorderRadius":0,"enableTextGradient":false,"textGradientType":"linear","textGradientStartColor":"#ffffff","textGradientEndColor":"#f0f0f0","textGradientAngle":0,"imageUrl":"","imageBorderRadius":0,"imageRotation":0,"imageOpacity":100,"imageObjectFit":"contain","decorationObjectFit":"contain","decorationOpacity":100,"decorationRotation":0,"decorationBlendMode":"normal","tableStyle":"table","tableSize":"table-normal","showTableHeader":true,"tableHeaderBgColor":"#f8f9fa","tableHeaderTextColor":"#212529","tableBodyBgColor":"#ffffff","tableBodyTextColor":"#212529","tableBorderColor":"#dee2e6","tableHeaderFontSize":14,"tableBodyFontSize":12,"tableFontWeight":"normal","enableTableCarousel":false,"tableRowsPerPage":5,"tableCarouselInterval":3,"showTablePagination":false,"hyperlinkUrl":"","hyperlinkText":"点击跳转","hyperlinkTarget":"_self","hyperlinkFontSize":16,"hyperlinkFontColor":"#007bff","hyperlinkFontWeight":"normal","hyperlinkTextAlign":"left","hyperlinkFontStyle":"normal","hyperlinkTextDecoration":"none","hyperlinkOpacity":100,"htmlOpacity":100,"videoUrl":"","poster":"","autoplay":false,"loop":false,"muted":true,"controls":true,"opacity":100,"borderWidth":0,"shadow":false,"timeFormat":"YYYY-MM-DD HH:mm:ss","timeFontSize":24,"timeFontWeight":"normal","timeFontStyle":"normal","timeColorType":"solid","timeColor":"#333333","timeGradientStart":"#667eea","timeGradientEnd":"#764ba2","timeGradientDirection":"to right","timeOpacity":100,"timeTextShadow":false}
bi-dashboard-designer.js?v=20250731-export-debug:4684 === 样式配置更新完成 ===
bi-dashboard-designer.js?v=20250731-export-debug:11759 === 样式配置生命周期追踪：实时应用后 ===
bi-dashboard-designer.js?v=20250731-export-debug:11760 组件ID: 3440
bi-dashboard-designer.js?v=20250731-export-debug:11761 组件类型: data-table
bi-dashboard-designer.js?v=20250731-export-debug:11762 当前styleConfig: {"theme":"default","backgroundColor":"#ffffff","borderColor":"#dee2e6","showTitle":true,"transparent":false,"titleFontSize":16,"showLegend":true,"showChartTitle":true,"borderlessMode":false,"showDataLabels":false,"showYAxis":true,"showYAxisLabels":true,"showXAxis":true,"yAxisFontSize":12,"yAxisFontColor":"#8c8c8c","xAxisFontSize":12,"xAxisFontColor":"#8c8c8c","dataLabelFontSize":11,"dataLabelFontColor":"#5470c6","legendFontSize":12,"legendFontColor":"#666666","lineWidth":3,"lineType":"solid","lineColor":"#5470c6","useGradientLine":false,"lineGradientColor":"#91cc75","smoothLine":true,"showArea":false,"useGradientArea":true,"areaStartColor":"#5470c6","areaMidColor":"#91cc75","areaEndColor":"#ffffff","showSymbol":true,"symbolType":"circle","symbolSize":8,"symbolColor":"#5470c6","enableShadow":false,"shadowBlur":10,"shadowOffsetY":3,"shadowColor":"#000000","multiLineSmooth":true,"multiLineShowSymbol":true,"multiLineSymbolSize":6,"multiLineWidth":2,"multiLineShowArea":false,"multiLineEnableDualYAxis":false,"useIndividualStyles":true,"lineCount":1,"individualLineStyles":[{"color":"#5470c6","width":2,"type":"solid","useGradient":false,"gradientColor":"#91cc75","showSymbol":true,"symbolSize":6,"symbolType":"circle","symbolColor":"#5470c6","symbolBorderColor":"#ffffff","symbolBorderWidth":0,"showArea":false,"areaOpacity":30,"showLabel":false,"labelPosition":"top","labelColor":"#333333","labelFontSize":12,"labelFontWeight":"normal"}],"barWidth":60,"borderRadius":0,"borderRadiusMode":"top","barColor":"#5470c6","useGradientBar":false,"barStartColor":"#5470c6","barEndColor":"#91cc75","gaugeMin":0,"gaugeMax":100,"gaugeUnit":"%","gaugeRadius":80,"pieType":"ring","pieRadius":70,"pieInnerRadius":40,"pieOuterRadius":70,"pieBorderRadius":0,"pieBorderRadiusMode":"all","showPieBorder":true,"pieShowDataLabels":true,"useCustomPieColors":false,"waterBorderWidth":5,"waterBorderColor":"#1890ff","useWaterBorderGradient":false,"waterBorderGradientColor":"#52c41a","waterWaveColor":"#1890ff","useWaterWaveGradient":false,"waterWaveGradientColor":"#52c41a","waterAmplitude":20,"showWaterLabel":true,"waterLabelFontSize":20,"waterLabelFontColor":"#333333","showPercentageSymbol":true,"columnBarWidth":40,"columnBorderRadius":8,"columnUseGradient":true,"columnStartColor":"#4facfe","columnEndColor":"#00f2fe","columnEnableShadow":true,"columnShadowColor":"#000000","columnShadowBlur":10,"columnShowDataLabel":true,"columnDataLabelPosition":"vertical-top","columnDataLabelColor":"#333333","columnDataLabelFontSize":14,"columnShowPercentage":true,"columnShowBarBackground":true,"columnBarBackgroundColor":"#f5f5f5","columnAllowOverflow":true,"textFontFamily":"Arial, sans-serif","textFontSize":16,"textFontColor":"#333333","textFontWeight":"normal","textFontStyle":"normal","textDecoration":"none","textAlign":"left","textVerticalAlign":"top","textLineHeight":1.5,"textLetterSpacing":0,"textPadding":10,"textBorderRadius":0,"enableTextGradient":false,"textGradientType":"linear","textGradientStartColor":"#ffffff","textGradientEndColor":"#f0f0f0","textGradientAngle":0,"imageUrl":"","imageBorderRadius":0,"imageRotation":0,"imageOpacity":100,"imageObjectFit":"contain","decorationObjectFit":"contain","decorationOpacity":100,"decorationRotation":0,"decorationBlendMode":"normal","tableStyle":"table","tableSize":"table-normal","showTableHeader":true,"tableHeaderBgColor":"#f8f9fa","tableHeaderTextColor":"#212529","tableBodyBgColor":"#ffffff","tableBodyTextColor":"#212529","tableBorderColor":"#dee2e6","tableHeaderFontSize":14,"tableBodyFontSize":12,"tableFontWeight":"normal","enableTableCarousel":false,"tableRowsPerPage":5,"tableCarouselInterval":3,"showTablePagination":false,"hyperlinkUrl":"","hyperlinkText":"点击跳转","hyperlinkTarget":"_self","hyperlinkFontSize":16,"hyperlinkFontColor":"#007bff","hyperlinkFontWeight":"normal","hyperlinkTextAlign":"left","hyperlinkFontStyle":"normal","hyperlinkTextDecoration":"none","hyperlinkOpacity":100,"htmlOpacity":100,"videoUrl":"","poster":"","autoplay":false,"loop":false,"muted":true,"controls":true,"opacity":100,"borderWidth":0,"shadow":false,"timeFormat":"YYYY-MM-DD HH:mm:ss","timeFontSize":24,"timeFontWeight":"normal","timeFontStyle":"normal","timeColorType":"solid","timeColor":"#333333","timeGradientStart":"#667eea","timeGradientEnd":"#764ba2","timeGradientDirection":"to right","timeOpacity":100,"timeTextShadow":false}
bi-dashboard-designer.js?v=20250731-export-debug:11767 解析后的样式配置: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250731-export-debug:11768 配置项数量: 169
bi-dashboard-designer.js?v=20250731-export-debug:11769 重要配置检查: {showDataLabels: false, primaryColor: undefined, theme: 'default'}
bi-dashboard-designer.js?v=20250731-export-debug:11782 附加信息: {formConfigKeys: Array(169), hasFormConfig: true}
bi-dashboard-designer.js?v=20250731-export-debug:11784 === 追踪结束 ===
bi-dashboard-designer.js?v=20250731-export-debug:4793 表格组件配置更新，重新加载数据和样式
bi-dashboard-designer.js?v=20250731-export-debug:10030 更新组件数据: 3440 data-table
bi-data-source-manager.js?v=20250127:913 === BiDataSourceManager - 开始获取组件数据 ===
bi-data-source-manager.js?v=20250127:914 组件信息: {id: 3440, type: 'data-table', dataSourceConfig: '{"dataSourceType":"externalData","multiDataSet":tr…nTargetDataItem":"","showColumnTargetLabel":true}'}
bi-data-source-manager.js?v=20250127:959 解析字符串格式数据源配置: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(0), refreshInterval: 5, mergeStrategy: 'union', …}
bi-data-source-manager.js?v=20250127:974 确定的数据源类型: externalData
bi-data-source-manager.js?v=20250127:975 完整数据源配置: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(0), refreshInterval: 5, mergeStrategy: 'union', …}
bi-data-source-manager.js?v=20250127:990 处理外部数据源...
bi-data-source-manager.js?v=20250127:2198 处理多外部数据源数据: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(0), refreshInterval: 5, mergeStrategy: 'union', …}
bi-data-source-manager.js?v=20250127:2360  处理多外部数据源失败: Error: 未配置数据集
    at BiDataSourceManager.processMultiExternalData (bi-data-source-manager.js?v=20250127:2295:23)
    at BiDataSourceManager.processExternalData (bi-data-source-manager.js?v=20250127:2199:35)
    at BiDataSourceManager.fetchWidgetData (bi-data-source-manager.js?v=20250127:991:41)
    at updateWidgetData (bi-dashboard-designer.js?v=20250731-export-debug:10035:46)
    at applyPropertiesRealTime (bi-dashboard-designer.js?v=20250731-export-debug:4811:17)
    at onMultiExternalDataSetToggle (bi-dashboard-designer.js?v=20250731-export-debug:15303:5)
    at HTMLInputElement.onchange (design:2478:168)
processMultiExternalData @ bi-data-source-manager.js?v=20250127:2360
processExternalData @ bi-data-source-manager.js?v=20250127:2199
fetchWidgetData @ bi-data-source-manager.js?v=20250127:991
updateWidgetData @ bi-dashboard-designer.js?v=20250731-export-debug:10035
applyPropertiesRealTime @ bi-dashboard-designer.js?v=20250731-export-debug:4811
onMultiExternalDataSetToggle @ bi-dashboard-designer.js?v=20250731-export-debug:15303
onchange @ design:2478
bi-dashboard-designer.js?v=20250731-export-debug:12752 组件 3440 自动刷新条件检查: {dataSourceType: 'externalData', hasDataItemId: false, hasDataSetId: false, isMultiDataSet: true, dataSetsCount: 0, …}
bi-dashboard-designer.js?v=20250731-export-debug:12826 组件 3440 不满足自动刷新条件，但仍尝试初始数据加载
bi-dashboard-designer.js?v=20250731-export-debug:12834 组件 3440 检测到数据源配置，执行初始数据加载
bi-dashboard-designer.js?v=20250731-export-debug:4836 实时应用属性: {id: 3440, type: 'data-table', x: 116, y: 318, width: 300, …}
bi-data-source-manager.js?v=20250127:1000 数据获取结果: {success: false, message: '未配置数据集', labels: Array(1), values: Array(1), data: Array(1)}
bi-data-source-manager.js?v=20250127:1001 === BiDataSourceManager - 数据获取完成 ===
bi-dashboard-designer.js?v=20250731-export-debug:10044  数据获取失败: undefined
updateWidgetData @ bi-dashboard-designer.js?v=20250731-export-debug:10044
await in updateWidgetData
applyPropertiesRealTime @ bi-dashboard-designer.js?v=20250731-export-debug:4811
onMultiExternalDataSetToggle @ bi-dashboard-designer.js?v=20250731-export-debug:15303
onchange @ design:2478
bi-dashboard-designer.js?v=20250731-export-debug:14503 数据集列表加载成功，共 5 个数据集
bi-dashboard-designer.js?v=20250731-export-debug:12860 安全更新组件数据: 3440 data-table
bi-dashboard-designer.js?v=20250731-export-debug:10030 更新组件数据: 3440 data-table
bi-data-source-manager.js?v=20250127:913 === BiDataSourceManager - 开始获取组件数据 ===
bi-data-source-manager.js?v=20250127:914 组件信息: {id: 3440, type: 'data-table', dataSourceConfig: '{"dataSourceType":"externalData","multiDataSet":tr…nTargetDataItem":"","showColumnTargetLabel":true}'}
bi-data-source-manager.js?v=20250127:959 解析字符串格式数据源配置: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(0), refreshInterval: 5, mergeStrategy: 'union', …}
bi-data-source-manager.js?v=20250127:974 确定的数据源类型: externalData
bi-data-source-manager.js?v=20250127:975 完整数据源配置: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(0), refreshInterval: 5, mergeStrategy: 'union', …}
bi-data-source-manager.js?v=20250127:990 处理外部数据源...
bi-data-source-manager.js?v=20250127:2198 处理多外部数据源数据: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(0), refreshInterval: 5, mergeStrategy: 'union', …}
bi-data-source-manager.js?v=20250127:2360  处理多外部数据源失败: Error: 未配置数据集
    at BiDataSourceManager.processMultiExternalData (bi-data-source-manager.js?v=20250127:2295:23)
    at BiDataSourceManager.processExternalData (bi-data-source-manager.js?v=20250127:2199:35)
    at BiDataSourceManager.fetchWidgetData (bi-data-source-manager.js?v=20250127:991:41)
    at updateWidgetData (bi-dashboard-designer.js?v=20250731-export-debug:10035:46)
    at updateWidgetDataSafe (bi-dashboard-designer.js?v=20250731-export-debug:12871:15)
    at waitForChartInitFallback (bi-dashboard-designer.js?v=20250731-export-debug:12849:17)
processMultiExternalData @ bi-data-source-manager.js?v=20250127:2360
processExternalData @ bi-data-source-manager.js?v=20250127:2199
fetchWidgetData @ bi-data-source-manager.js?v=20250127:991
updateWidgetData @ bi-dashboard-designer.js?v=20250731-export-debug:10035
updateWidgetDataSafe @ bi-dashboard-designer.js?v=20250731-export-debug:12871
waitForChartInitFallback @ bi-dashboard-designer.js?v=20250731-export-debug:12849
setTimeout
setupWidgetAutoRefresh @ bi-dashboard-designer.js?v=20250731-export-debug:12853
applyPropertiesRealTime @ bi-dashboard-designer.js?v=20250731-export-debug:4824
onMultiExternalDataSetToggle @ bi-dashboard-designer.js?v=20250731-export-debug:15303
onchange @ design:2478
bi-dashboard-designer.js?v=20250731-export-debug:12850 组件 3440 兜底数据加载完成
bi-data-source-manager.js?v=20250127:1000 数据获取结果: {success: false, message: '未配置数据集', labels: Array(1), values: Array(1), data: Array(1)}
bi-data-source-manager.js?v=20250127:1001 === BiDataSourceManager - 数据获取完成 ===
bi-dashboard-designer.js?v=20250731-export-debug:10044  数据获取失败: undefined
updateWidgetData @ bi-dashboard-designer.js?v=20250731-export-debug:10044
await in updateWidgetData
updateWidgetDataSafe @ bi-dashboard-designer.js?v=20250731-export-debug:12871
waitForChartInitFallback @ bi-dashboard-designer.js?v=20250731-export-debug:12849
setTimeout
setupWidgetAutoRefresh @ bi-dashboard-designer.js?v=20250731-export-debug:12853
applyPropertiesRealTime @ bi-dashboard-designer.js?v=20250731-export-debug:4824
onMultiExternalDataSetToggle @ bi-dashboard-designer.js?v=20250731-export-debug:15303
onchange @ design:2478
bi-dashboard-designer.js?v=20250731-export-debug:15645 通过元素触发的数据集选择变化事件
bi-dashboard-designer.js?v=20250731-export-debug:15654 数据集选择变化，数据集ID: dataset_1749946359215，容器ID: externalDataSet_0
bi-dashboard-designer.js?v=20250731-export-debug:464 组件 3440 (data-table) 已标记为修改: properties
bi-dashboard-designer.js?v=20250731-export-debug:4042 缓存组件 3440 配置，时间戳: 2025-08-01T06:09:03.995Z
bi-dashboard-designer.js?v=20250731-export-debug:532 同步组件 3440 配置缓存
bi-data-source-manager.js?v=20250127:207 已清理组件 3440 的配置上下文
bi-data-source-manager.js?v=20250127:179 为组件 3440 (data-table) 创建配置上下文
bi-data-source-manager.js?v=20250127:249 为组件 3440 缓存DOM元素: dataSourceType
bi-data-source-manager.js?v=20250127:1451 收集组件 3440 (data-table) 外部数据源配置，多数据集模式: true
bi-data-source-manager.js?v=20250127:1513 收集多数据集配置 - 容器ID: tableExternalDataSourceList
bi-data-source-manager.js?v=20250127:1514 收集多数据集配置 - 找到数据集项数量: 1
bi-data-source-manager.js?v=20250127:1523 数据集项 DOM索引0: {itemId: 'externalDataSet_0', dataSetId: 'dataset_1749946359215', dataSetName: 'ttt1', labelField: 'null', valueField: 'null', …}
bi-data-source-manager.js?v=20250127:1564 添加数据集配置 DOM索引0: {dataSetId: 'dataset_1749946359215', dataSetName: 'ttt1', alias: '数据集1', domIndex: 0, tableFields: Array(0)}
bi-data-source-manager.js?v=20250127:1571 最终收集到的数据集配置: [{…}]
bi-data-source-manager.js?v=20250127:1456 组件 3440 收集到多数据集配置，数据集数量: 1
bi-data-source-manager.js?v=20250127:429 组件 3440 数据源配置收集完成: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(1), refreshInterval: 5, mergeStrategy: 'union'}
bi-dashboard-designer.js?v=20250731-export-debug:11789 === 开始收集样式配置 ===
bi-dashboard-designer.js?v=20250731-export-debug:11405 🔧 [修复版本 v2.0] 开始收集多折线样式配置
bi-dashboard-designer.js?v=20250731-export-debug:11412 🔧 [修复版本] 折线数量: 1 (来源: 实际数据 )
bi-dashboard-designer.js?v=20250731-export-debug:11423 收集折线 1 的样式配置
bi-dashboard-designer.js?v=20250731-export-debug:11457 收集到的多折线样式配置: [{…}]
bi-dashboard-designer.js?v=20250731-export-debug:12484 === 样式配置收集完成 ===
bi-dashboard-designer.js?v=20250731-export-debug:12485 收集到的配置项数量: 169
bi-dashboard-designer.js?v=20250731-export-debug:12486 重要配置项检查: {showDataLabels: false, primaryColor: undefined, theme: 'default', showChartTitle: true, showLegend: true, …}
bi-dashboard-designer.js?v=20250731-export-debug:12502 完整样式配置: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250731-export-debug:4670 === 实时应用属性：样式配置更新 ===
bi-dashboard-designer.js?v=20250731-export-debug:4671 组件ID: 3440
bi-dashboard-designer.js?v=20250731-export-debug:4672 组件类型: data-table
bi-dashboard-designer.js?v=20250731-export-debug:4673 更新前styleConfig: {"theme":"default","backgroundColor":"#ffffff","borderColor":"#dee2e6","showTitle":true,"transparent":false,"titleFontSize":16,"showLegend":true,"showChartTitle":true,"borderlessMode":false,"showDataLabels":false,"showYAxis":true,"showYAxisLabels":true,"showXAxis":true,"yAxisFontSize":12,"yAxisFontColor":"#8c8c8c","xAxisFontSize":12,"xAxisFontColor":"#8c8c8c","dataLabelFontSize":11,"dataLabelFontColor":"#5470c6","legendFontSize":12,"legendFontColor":"#666666","lineWidth":3,"lineType":"solid","lineColor":"#5470c6","useGradientLine":false,"lineGradientColor":"#91cc75","smoothLine":true,"showArea":false,"useGradientArea":true,"areaStartColor":"#5470c6","areaMidColor":"#91cc75","areaEndColor":"#ffffff","showSymbol":true,"symbolType":"circle","symbolSize":8,"symbolColor":"#5470c6","enableShadow":false,"shadowBlur":10,"shadowOffsetY":3,"shadowColor":"#000000","multiLineSmooth":true,"multiLineShowSymbol":true,"multiLineSymbolSize":6,"multiLineWidth":2,"multiLineShowArea":false,"multiLineEnableDualYAxis":false,"useIndividualStyles":true,"lineCount":1,"individualLineStyles":[{"color":"#5470c6","width":2,"type":"solid","useGradient":false,"gradientColor":"#91cc75","showSymbol":true,"symbolSize":6,"symbolType":"circle","symbolColor":"#5470c6","symbolBorderColor":"#ffffff","symbolBorderWidth":0,"showArea":false,"areaOpacity":30,"showLabel":false,"labelPosition":"top","labelColor":"#333333","labelFontSize":12,"labelFontWeight":"normal"}],"barWidth":60,"borderRadius":0,"borderRadiusMode":"top","barColor":"#5470c6","useGradientBar":false,"barStartColor":"#5470c6","barEndColor":"#91cc75","gaugeMin":0,"gaugeMax":100,"gaugeUnit":"%","gaugeRadius":80,"pieType":"ring","pieRadius":70,"pieInnerRadius":40,"pieOuterRadius":70,"pieBorderRadius":0,"pieBorderRadiusMode":"all","showPieBorder":true,"pieShowDataLabels":true,"useCustomPieColors":false,"waterBorderWidth":5,"waterBorderColor":"#1890ff","useWaterBorderGradient":false,"waterBorderGradientColor":"#52c41a","waterWaveColor":"#1890ff","useWaterWaveGradient":false,"waterWaveGradientColor":"#52c41a","waterAmplitude":20,"showWaterLabel":true,"waterLabelFontSize":20,"waterLabelFontColor":"#333333","showPercentageSymbol":true,"columnBarWidth":40,"columnBorderRadius":8,"columnUseGradient":true,"columnStartColor":"#4facfe","columnEndColor":"#00f2fe","columnEnableShadow":true,"columnShadowColor":"#000000","columnShadowBlur":10,"columnShowDataLabel":true,"columnDataLabelPosition":"vertical-top","columnDataLabelColor":"#333333","columnDataLabelFontSize":14,"columnShowPercentage":true,"columnShowBarBackground":true,"columnBarBackgroundColor":"#f5f5f5","columnAllowOverflow":true,"textFontFamily":"Arial, sans-serif","textFontSize":16,"textFontColor":"#333333","textFontWeight":"normal","textFontStyle":"normal","textDecoration":"none","textAlign":"left","textVerticalAlign":"top","textLineHeight":1.5,"textLetterSpacing":0,"textPadding":10,"textBorderRadius":0,"enableTextGradient":false,"textGradientType":"linear","textGradientStartColor":"#ffffff","textGradientEndColor":"#f0f0f0","textGradientAngle":0,"imageUrl":"","imageBorderRadius":0,"imageRotation":0,"imageOpacity":100,"imageObjectFit":"contain","decorationObjectFit":"contain","decorationOpacity":100,"decorationRotation":0,"decorationBlendMode":"normal","tableStyle":"table","tableSize":"table-normal","showTableHeader":true,"tableHeaderBgColor":"#f8f9fa","tableHeaderTextColor":"#212529","tableBodyBgColor":"#ffffff","tableBodyTextColor":"#212529","tableBorderColor":"#dee2e6","tableHeaderFontSize":14,"tableBodyFontSize":12,"tableFontWeight":"normal","enableTableCarousel":false,"tableRowsPerPage":5,"tableCarouselInterval":3,"showTablePagination":false,"hyperlinkUrl":"","hyperlinkText":"点击跳转","hyperlinkTarget":"_self","hyperlinkFontSize":16,"hyperlinkFontColor":"#007bff","hyperlinkFontWeight":"normal","hyperlinkTextAlign":"left","hyperlinkFontStyle":"normal","hyperlinkTextDecoration":"none","hyperlinkOpacity":100,"htmlOpacity":100,"videoUrl":"","poster":"","autoplay":false,"loop":false,"muted":true,"controls":true,"opacity":100,"borderWidth":0,"shadow":false,"timeFormat":"YYYY-MM-DD HH:mm:ss","timeFontSize":24,"timeFontWeight":"normal","timeFontStyle":"normal","timeColorType":"solid","timeColor":"#333333","timeGradientStart":"#667eea","timeGradientEnd":"#764ba2","timeGradientDirection":"to right","timeOpacity":100,"timeTextShadow":false}
bi-dashboard-designer.js?v=20250731-export-debug:4674 新的styleConfig: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250731-export-debug:4683 更新后styleConfig字符串: {"theme":"default","backgroundColor":"#ffffff","borderColor":"#dee2e6","showTitle":true,"transparent":false,"titleFontSize":16,"showLegend":true,"showChartTitle":true,"borderlessMode":false,"showDataLabels":false,"showYAxis":true,"showYAxisLabels":true,"showXAxis":true,"yAxisFontSize":12,"yAxisFontColor":"#8c8c8c","xAxisFontSize":12,"xAxisFontColor":"#8c8c8c","dataLabelFontSize":11,"dataLabelFontColor":"#5470c6","legendFontSize":12,"legendFontColor":"#666666","lineWidth":3,"lineType":"solid","lineColor":"#5470c6","useGradientLine":false,"lineGradientColor":"#91cc75","smoothLine":true,"showArea":false,"useGradientArea":true,"areaStartColor":"#5470c6","areaMidColor":"#91cc75","areaEndColor":"#ffffff","showSymbol":true,"symbolType":"circle","symbolSize":8,"symbolColor":"#5470c6","enableShadow":false,"shadowBlur":10,"shadowOffsetY":3,"shadowColor":"#000000","multiLineSmooth":true,"multiLineShowSymbol":true,"multiLineSymbolSize":6,"multiLineWidth":2,"multiLineShowArea":false,"multiLineEnableDualYAxis":false,"useIndividualStyles":true,"lineCount":1,"individualLineStyles":[{"color":"#5470c6","width":2,"type":"solid","useGradient":false,"gradientColor":"#91cc75","showSymbol":true,"symbolSize":6,"symbolType":"circle","symbolColor":"#5470c6","symbolBorderColor":"#ffffff","symbolBorderWidth":0,"showArea":false,"areaOpacity":30,"showLabel":false,"labelPosition":"top","labelColor":"#333333","labelFontSize":12,"labelFontWeight":"normal"}],"barWidth":60,"borderRadius":0,"borderRadiusMode":"top","barColor":"#5470c6","useGradientBar":false,"barStartColor":"#5470c6","barEndColor":"#91cc75","gaugeMin":0,"gaugeMax":100,"gaugeUnit":"%","gaugeRadius":80,"pieType":"ring","pieRadius":70,"pieInnerRadius":40,"pieOuterRadius":70,"pieBorderRadius":0,"pieBorderRadiusMode":"all","showPieBorder":true,"pieShowDataLabels":true,"useCustomPieColors":false,"waterBorderWidth":5,"waterBorderColor":"#1890ff","useWaterBorderGradient":false,"waterBorderGradientColor":"#52c41a","waterWaveColor":"#1890ff","useWaterWaveGradient":false,"waterWaveGradientColor":"#52c41a","waterAmplitude":20,"showWaterLabel":true,"waterLabelFontSize":20,"waterLabelFontColor":"#333333","showPercentageSymbol":true,"columnBarWidth":40,"columnBorderRadius":8,"columnUseGradient":true,"columnStartColor":"#4facfe","columnEndColor":"#00f2fe","columnEnableShadow":true,"columnShadowColor":"#000000","columnShadowBlur":10,"columnShowDataLabel":true,"columnDataLabelPosition":"vertical-top","columnDataLabelColor":"#333333","columnDataLabelFontSize":14,"columnShowPercentage":true,"columnShowBarBackground":true,"columnBarBackgroundColor":"#f5f5f5","columnAllowOverflow":true,"textFontFamily":"Arial, sans-serif","textFontSize":16,"textFontColor":"#333333","textFontWeight":"normal","textFontStyle":"normal","textDecoration":"none","textAlign":"left","textVerticalAlign":"top","textLineHeight":1.5,"textLetterSpacing":0,"textPadding":10,"textBorderRadius":0,"enableTextGradient":false,"textGradientType":"linear","textGradientStartColor":"#ffffff","textGradientEndColor":"#f0f0f0","textGradientAngle":0,"imageUrl":"","imageBorderRadius":0,"imageRotation":0,"imageOpacity":100,"imageObjectFit":"contain","decorationObjectFit":"contain","decorationOpacity":100,"decorationRotation":0,"decorationBlendMode":"normal","tableStyle":"table","tableSize":"table-normal","showTableHeader":true,"tableHeaderBgColor":"#f8f9fa","tableHeaderTextColor":"#212529","tableBodyBgColor":"#ffffff","tableBodyTextColor":"#212529","tableBorderColor":"#dee2e6","tableHeaderFontSize":14,"tableBodyFontSize":12,"tableFontWeight":"normal","enableTableCarousel":false,"tableRowsPerPage":5,"tableCarouselInterval":3,"showTablePagination":false,"hyperlinkUrl":"","hyperlinkText":"点击跳转","hyperlinkTarget":"_self","hyperlinkFontSize":16,"hyperlinkFontColor":"#007bff","hyperlinkFontWeight":"normal","hyperlinkTextAlign":"left","hyperlinkFontStyle":"normal","hyperlinkTextDecoration":"none","hyperlinkOpacity":100,"htmlOpacity":100,"videoUrl":"","poster":"","autoplay":false,"loop":false,"muted":true,"controls":true,"opacity":100,"borderWidth":0,"shadow":false,"timeFormat":"YYYY-MM-DD HH:mm:ss","timeFontSize":24,"timeFontWeight":"normal","timeFontStyle":"normal","timeColorType":"solid","timeColor":"#333333","timeGradientStart":"#667eea","timeGradientEnd":"#764ba2","timeGradientDirection":"to right","timeOpacity":100,"timeTextShadow":false}
bi-dashboard-designer.js?v=20250731-export-debug:4684 === 样式配置更新完成 ===
bi-dashboard-designer.js?v=20250731-export-debug:11759 === 样式配置生命周期追踪：实时应用后 ===
bi-dashboard-designer.js?v=20250731-export-debug:11760 组件ID: 3440
bi-dashboard-designer.js?v=20250731-export-debug:11761 组件类型: data-table
bi-dashboard-designer.js?v=20250731-export-debug:11762 当前styleConfig: {"theme":"default","backgroundColor":"#ffffff","borderColor":"#dee2e6","showTitle":true,"transparent":false,"titleFontSize":16,"showLegend":true,"showChartTitle":true,"borderlessMode":false,"showDataLabels":false,"showYAxis":true,"showYAxisLabels":true,"showXAxis":true,"yAxisFontSize":12,"yAxisFontColor":"#8c8c8c","xAxisFontSize":12,"xAxisFontColor":"#8c8c8c","dataLabelFontSize":11,"dataLabelFontColor":"#5470c6","legendFontSize":12,"legendFontColor":"#666666","lineWidth":3,"lineType":"solid","lineColor":"#5470c6","useGradientLine":false,"lineGradientColor":"#91cc75","smoothLine":true,"showArea":false,"useGradientArea":true,"areaStartColor":"#5470c6","areaMidColor":"#91cc75","areaEndColor":"#ffffff","showSymbol":true,"symbolType":"circle","symbolSize":8,"symbolColor":"#5470c6","enableShadow":false,"shadowBlur":10,"shadowOffsetY":3,"shadowColor":"#000000","multiLineSmooth":true,"multiLineShowSymbol":true,"multiLineSymbolSize":6,"multiLineWidth":2,"multiLineShowArea":false,"multiLineEnableDualYAxis":false,"useIndividualStyles":true,"lineCount":1,"individualLineStyles":[{"color":"#5470c6","width":2,"type":"solid","useGradient":false,"gradientColor":"#91cc75","showSymbol":true,"symbolSize":6,"symbolType":"circle","symbolColor":"#5470c6","symbolBorderColor":"#ffffff","symbolBorderWidth":0,"showArea":false,"areaOpacity":30,"showLabel":false,"labelPosition":"top","labelColor":"#333333","labelFontSize":12,"labelFontWeight":"normal"}],"barWidth":60,"borderRadius":0,"borderRadiusMode":"top","barColor":"#5470c6","useGradientBar":false,"barStartColor":"#5470c6","barEndColor":"#91cc75","gaugeMin":0,"gaugeMax":100,"gaugeUnit":"%","gaugeRadius":80,"pieType":"ring","pieRadius":70,"pieInnerRadius":40,"pieOuterRadius":70,"pieBorderRadius":0,"pieBorderRadiusMode":"all","showPieBorder":true,"pieShowDataLabels":true,"useCustomPieColors":false,"waterBorderWidth":5,"waterBorderColor":"#1890ff","useWaterBorderGradient":false,"waterBorderGradientColor":"#52c41a","waterWaveColor":"#1890ff","useWaterWaveGradient":false,"waterWaveGradientColor":"#52c41a","waterAmplitude":20,"showWaterLabel":true,"waterLabelFontSize":20,"waterLabelFontColor":"#333333","showPercentageSymbol":true,"columnBarWidth":40,"columnBorderRadius":8,"columnUseGradient":true,"columnStartColor":"#4facfe","columnEndColor":"#00f2fe","columnEnableShadow":true,"columnShadowColor":"#000000","columnShadowBlur":10,"columnShowDataLabel":true,"columnDataLabelPosition":"vertical-top","columnDataLabelColor":"#333333","columnDataLabelFontSize":14,"columnShowPercentage":true,"columnShowBarBackground":true,"columnBarBackgroundColor":"#f5f5f5","columnAllowOverflow":true,"textFontFamily":"Arial, sans-serif","textFontSize":16,"textFontColor":"#333333","textFontWeight":"normal","textFontStyle":"normal","textDecoration":"none","textAlign":"left","textVerticalAlign":"top","textLineHeight":1.5,"textLetterSpacing":0,"textPadding":10,"textBorderRadius":0,"enableTextGradient":false,"textGradientType":"linear","textGradientStartColor":"#ffffff","textGradientEndColor":"#f0f0f0","textGradientAngle":0,"imageUrl":"","imageBorderRadius":0,"imageRotation":0,"imageOpacity":100,"imageObjectFit":"contain","decorationObjectFit":"contain","decorationOpacity":100,"decorationRotation":0,"decorationBlendMode":"normal","tableStyle":"table","tableSize":"table-normal","showTableHeader":true,"tableHeaderBgColor":"#f8f9fa","tableHeaderTextColor":"#212529","tableBodyBgColor":"#ffffff","tableBodyTextColor":"#212529","tableBorderColor":"#dee2e6","tableHeaderFontSize":14,"tableBodyFontSize":12,"tableFontWeight":"normal","enableTableCarousel":false,"tableRowsPerPage":5,"tableCarouselInterval":3,"showTablePagination":false,"hyperlinkUrl":"","hyperlinkText":"点击跳转","hyperlinkTarget":"_self","hyperlinkFontSize":16,"hyperlinkFontColor":"#007bff","hyperlinkFontWeight":"normal","hyperlinkTextAlign":"left","hyperlinkFontStyle":"normal","hyperlinkTextDecoration":"none","hyperlinkOpacity":100,"htmlOpacity":100,"videoUrl":"","poster":"","autoplay":false,"loop":false,"muted":true,"controls":true,"opacity":100,"borderWidth":0,"shadow":false,"timeFormat":"YYYY-MM-DD HH:mm:ss","timeFontSize":24,"timeFontWeight":"normal","timeFontStyle":"normal","timeColorType":"solid","timeColor":"#333333","timeGradientStart":"#667eea","timeGradientEnd":"#764ba2","timeGradientDirection":"to right","timeOpacity":100,"timeTextShadow":false}
bi-dashboard-designer.js?v=20250731-export-debug:11767 解析后的样式配置: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250731-export-debug:11768 配置项数量: 169
bi-dashboard-designer.js?v=20250731-export-debug:11769 重要配置检查: {showDataLabels: false, primaryColor: undefined, theme: 'default'}
bi-dashboard-designer.js?v=20250731-export-debug:11782 附加信息: {formConfigKeys: Array(169), hasFormConfig: true}
bi-dashboard-designer.js?v=20250731-export-debug:11784 === 追踪结束 ===
bi-dashboard-designer.js?v=20250731-export-debug:4793 表格组件配置更新，重新加载数据和样式
bi-dashboard-designer.js?v=20250731-export-debug:10030 更新组件数据: 3440 data-table
bi-data-source-manager.js?v=20250127:913 === BiDataSourceManager - 开始获取组件数据 ===
bi-data-source-manager.js?v=20250127:914 组件信息: {id: 3440, type: 'data-table', dataSourceConfig: '{"dataSourceType":"externalData","multiDataSet":tr…nTargetDataItem":"","showColumnTargetLabel":true}'}
bi-data-source-manager.js?v=20250127:959 解析字符串格式数据源配置: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(1), refreshInterval: 5, mergeStrategy: 'union', …}
bi-data-source-manager.js?v=20250127:974 确定的数据源类型: externalData
bi-data-source-manager.js?v=20250127:975 完整数据源配置: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(1), refreshInterval: 5, mergeStrategy: 'union', …}
bi-data-source-manager.js?v=20250127:990 处理外部数据源...
bi-data-source-manager.js?v=20250127:2198 处理多外部数据源数据: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(1), refreshInterval: 5, mergeStrategy: 'union', …}
bi-data-source-manager.js?v=20250127:2298 处理多外部数据源，数据集数量: 1
bi-data-source-manager.js?v=20250127:2306 获取数据集 1 (实际索引: 0): {dataSetId: 'dataset_1749946359215', dataSetName: 'ttt1', alias: '数据集1', domIndex: 0, tableFields: Array(0)}
bi-dashboard-designer.js?v=20250731-export-debug:12752 组件 3440 自动刷新条件检查: {dataSourceType: 'externalData', hasDataItemId: false, hasDataSetId: false, isMultiDataSet: true, dataSetsCount: 1, …}
bi-dashboard-designer.js?v=20250731-export-debug:12768 设置组件 3440 自动刷新间隔: 5000ms (数据源类型: externalData)
bi-dashboard-designer.js?v=20250731-export-debug:4836 实时应用属性: {id: 3440, type: 'data-table', x: 116, y: 318, width: 300, …}
bi-data-source-manager.js?v=20250127:2314   POST http://localhost:8080/api/bi/dataset/dataset_1749946359215/data 500 (Internal Server Error)
(匿名) @ bi-data-source-manager.js?v=20250127:2314
processMultiExternalData @ bi-data-source-manager.js?v=20250127:2304
processExternalData @ bi-data-source-manager.js?v=20250127:2199
fetchWidgetData @ bi-data-source-manager.js?v=20250127:991
updateWidgetData @ bi-dashboard-designer.js?v=20250731-export-debug:10035
applyPropertiesRealTime @ bi-dashboard-designer.js?v=20250731-export-debug:4811
onExternalDataSetChangeByElement @ bi-dashboard-designer.js?v=20250731-export-debug:15671
onchange @ design:1
handleMouseUp_ @ 未知
bi-data-source-manager.js?v=20250127:2354 所有数据集查询完成: [{…}]
bi-data-source-manager.js?v=20250127:2712 合并多外部数据源数据: {results: Array(1), mergeStrategy: 'union', componentType: 'data-table'}
bi-data-source-manager.js?v=20250127:1000 数据获取结果: {success: false, message: '所有数据集查询失败: 数据集1: 服务器错误', labels: Array(1), values: Array(1), data: Array(1)}
bi-data-source-manager.js?v=20250127:1001 === BiDataSourceManager - 数据获取完成 ===
bi-dashboard-designer.js?v=20250731-export-debug:10044  数据获取失败: undefined
updateWidgetData @ bi-dashboard-designer.js?v=20250731-export-debug:10044
await in updateWidgetData
applyPropertiesRealTime @ bi-dashboard-designer.js?v=20250731-export-debug:4811
onExternalDataSetChangeByElement @ bi-dashboard-designer.js?v=20250731-export-debug:15671
onchange @ design:1
handleMouseUp_ @ 未知
bi-dashboard-designer.js?v=20250731-export-debug:15992 updateTableFieldOptionsForDataSet - API响应: {success: true, fields: Array(2)}
bi-dashboard-designer.js?v=20250731-export-debug:12786 组件 3440 开始首次数据更新，数据源类型: externalData
bi-dashboard-designer.js?v=20250731-export-debug:12860 安全更新组件数据: 3440 data-table
bi-dashboard-designer.js?v=20250731-export-debug:10030 更新组件数据: 3440 data-table
bi-data-source-manager.js?v=20250127:913 === BiDataSourceManager - 开始获取组件数据 ===
bi-data-source-manager.js?v=20250127:914 组件信息: {id: 3440, type: 'data-table', dataSourceConfig: '{"dataSourceType":"externalData","multiDataSet":tr…nTargetDataItem":"","showColumnTargetLabel":true}'}
bi-data-source-manager.js?v=20250127:959 解析字符串格式数据源配置: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(1), refreshInterval: 5, mergeStrategy: 'union', …}
bi-data-source-manager.js?v=20250127:974 确定的数据源类型: externalData
bi-data-source-manager.js?v=20250127:975 完整数据源配置: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(1), refreshInterval: 5, mergeStrategy: 'union', …}
bi-data-source-manager.js?v=20250127:990 处理外部数据源...
bi-data-source-manager.js?v=20250127:2198 处理多外部数据源数据: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(1), refreshInterval: 5, mergeStrategy: 'union', …}
bi-data-source-manager.js?v=20250127:2298 处理多外部数据源，数据集数量: 1
bi-data-source-manager.js?v=20250127:2306 获取数据集 1 (实际索引: 0): {dataSetId: 'dataset_1749946359215', dataSetName: 'ttt1', alias: '数据集1', domIndex: 0, tableFields: Array(0)}
bi-dashboard-designer.js?v=20250731-export-debug:12788 组件 3440 首次数据更新完成
bi-data-source-manager.js?v=20250127:2314   POST http://localhost:8080/api/bi/dataset/dataset_1749946359215/data 500 (Internal Server Error)
(匿名) @ bi-data-source-manager.js?v=20250127:2314
processMultiExternalData @ bi-data-source-manager.js?v=20250127:2304
processExternalData @ bi-data-source-manager.js?v=20250127:2199
fetchWidgetData @ bi-data-source-manager.js?v=20250127:991
updateWidgetData @ bi-dashboard-designer.js?v=20250731-export-debug:10035
updateWidgetDataSafe @ bi-dashboard-designer.js?v=20250731-export-debug:12871
waitForChartInit @ bi-dashboard-designer.js?v=20250731-export-debug:12787
setTimeout
setupWidgetAutoRefresh @ bi-dashboard-designer.js?v=20250731-export-debug:12792
applyPropertiesRealTime @ bi-dashboard-designer.js?v=20250731-export-debug:4824
onExternalDataSetChangeByElement @ bi-dashboard-designer.js?v=20250731-export-debug:15671
onchange @ design:1
handleMouseUp_ @ 未知
bi-data-source-manager.js?v=20250127:2354 所有数据集查询完成: [{…}]
bi-data-source-manager.js?v=20250127:2712 合并多外部数据源数据: {results: Array(1), mergeStrategy: 'union', componentType: 'data-table'}
bi-data-source-manager.js?v=20250127:1000 数据获取结果: {success: false, message: '所有数据集查询失败: 数据集1: 服务器错误', labels: Array(1), values: Array(1), data: Array(1)}
bi-data-source-manager.js?v=20250127:1001 === BiDataSourceManager - 数据获取完成 ===
bi-dashboard-designer.js?v=20250731-export-debug:10044  数据获取失败: undefined
updateWidgetData @ bi-dashboard-designer.js?v=20250731-export-debug:10044
await in updateWidgetData
updateWidgetDataSafe @ bi-dashboard-designer.js?v=20250731-export-debug:12871
waitForChartInit @ bi-dashboard-designer.js?v=20250731-export-debug:12787
setTimeout
setupWidgetAutoRefresh @ bi-dashboard-designer.js?v=20250731-export-debug:12792
applyPropertiesRealTime @ bi-dashboard-designer.js?v=20250731-export-debug:4824
onExternalDataSetChangeByElement @ bi-dashboard-designer.js?v=20250731-export-debug:15671
onchange @ design:1
handleMouseUp_ @ 未知
bi-dashboard-designer.js?v=20250731-export-debug:16113 loadFieldOptionsForDataSet - API响应: {success: true, fields: Array(2)}
bi-dashboard-designer.js?v=20250731-export-debug:16117 字段选项 0: {displayName: 'name', name: 'name', description: '文本字段，适合作为X轴标签', type: 'text', sampleValue: '拔楦数量', …}
bi-dashboard-designer.js?v=20250731-export-debug:16117 字段选项 1: {displayName: 'latest_value', name: 'latest_value', description: '数值字段，适合作为Y轴数据', type: 'number', sampleValue: '36', …}
bi-dashboard-designer.js?v=20250731-export-debug:464 组件 3440 (data-table) 已标记为修改: properties
bi-dashboard-designer.js?v=20250731-export-debug:4042 缓存组件 3440 配置，时间戳: 2025-08-01T06:09:06.388Z
bi-dashboard-designer.js?v=20250731-export-debug:532 同步组件 3440 配置缓存
bi-data-source-manager.js?v=20250127:207 已清理组件 3440 的配置上下文
bi-data-source-manager.js?v=20250127:179 为组件 3440 (data-table) 创建配置上下文
bi-data-source-manager.js?v=20250127:249 为组件 3440 缓存DOM元素: dataSourceType
bi-data-source-manager.js?v=20250127:1451 收集组件 3440 (data-table) 外部数据源配置，多数据集模式: true
bi-data-source-manager.js?v=20250127:1513 收集多数据集配置 - 容器ID: tableExternalDataSourceList
bi-data-source-manager.js?v=20250127:1514 收集多数据集配置 - 找到数据集项数量: 1
bi-data-source-manager.js?v=20250127:1523 数据集项 DOM索引0: {itemId: 'externalDataSet_0', dataSetId: 'dataset_1749946359215', dataSetName: 'ttt1', labelField: 'null', valueField: 'null', …}
bi-data-source-manager.js?v=20250127:1645 collectTableFieldConfigFromContainer: 字段卡片 1: {cardId: 'tableField_dataSet_0_field_0', nameInput: 'tableFieldName_dataSet_0_field_0', mappingSelect: 'tableFieldMapping_dataSet_0_field_0', nameValue: '', mappingValue: 'latest_value'}
bi-data-source-manager.js?v=20250127:1661 collectTableFieldConfigFromContainer: 字段卡片 1 配置不完整，跳过
bi-data-source-manager.js?v=20250127:1564 添加数据集配置 DOM索引0: {dataSetId: 'dataset_1749946359215', dataSetName: 'ttt1', alias: '数据集1', domIndex: 0, tableFields: Array(0)}
bi-data-source-manager.js?v=20250127:1571 最终收集到的数据集配置: [{…}]
bi-data-source-manager.js?v=20250127:1456 组件 3440 收集到多数据集配置，数据集数量: 1
bi-data-source-manager.js?v=20250127:429 组件 3440 数据源配置收集完成: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(1), refreshInterval: 5, mergeStrategy: 'union'}
bi-dashboard-designer.js?v=20250731-export-debug:11789 === 开始收集样式配置 ===
bi-dashboard-designer.js?v=20250731-export-debug:11405 🔧 [修复版本 v2.0] 开始收集多折线样式配置
bi-dashboard-designer.js?v=20250731-export-debug:11412 🔧 [修复版本] 折线数量: 1 (来源: 实际数据 )
bi-dashboard-designer.js?v=20250731-export-debug:11423 收集折线 1 的样式配置
bi-dashboard-designer.js?v=20250731-export-debug:11457 收集到的多折线样式配置: [{…}]
bi-dashboard-designer.js?v=20250731-export-debug:12484 === 样式配置收集完成 ===
bi-dashboard-designer.js?v=20250731-export-debug:12485 收集到的配置项数量: 169
bi-dashboard-designer.js?v=20250731-export-debug:12486 重要配置项检查: {showDataLabels: false, primaryColor: undefined, theme: 'default', showChartTitle: true, showLegend: true, …}
bi-dashboard-designer.js?v=20250731-export-debug:12502 完整样式配置: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250731-export-debug:4670 === 实时应用属性：样式配置更新 ===
bi-dashboard-designer.js?v=20250731-export-debug:4671 组件ID: 3440
bi-dashboard-designer.js?v=20250731-export-debug:4672 组件类型: data-table
bi-dashboard-designer.js?v=20250731-export-debug:4673 更新前styleConfig: {"theme":"default","backgroundColor":"#ffffff","borderColor":"#dee2e6","showTitle":true,"transparent":false,"titleFontSize":16,"showLegend":true,"showChartTitle":true,"borderlessMode":false,"showDataLabels":false,"showYAxis":true,"showYAxisLabels":true,"showXAxis":true,"yAxisFontSize":12,"yAxisFontColor":"#8c8c8c","xAxisFontSize":12,"xAxisFontColor":"#8c8c8c","dataLabelFontSize":11,"dataLabelFontColor":"#5470c6","legendFontSize":12,"legendFontColor":"#666666","lineWidth":3,"lineType":"solid","lineColor":"#5470c6","useGradientLine":false,"lineGradientColor":"#91cc75","smoothLine":true,"showArea":false,"useGradientArea":true,"areaStartColor":"#5470c6","areaMidColor":"#91cc75","areaEndColor":"#ffffff","showSymbol":true,"symbolType":"circle","symbolSize":8,"symbolColor":"#5470c6","enableShadow":false,"shadowBlur":10,"shadowOffsetY":3,"shadowColor":"#000000","multiLineSmooth":true,"multiLineShowSymbol":true,"multiLineSymbolSize":6,"multiLineWidth":2,"multiLineShowArea":false,"multiLineEnableDualYAxis":false,"useIndividualStyles":true,"lineCount":1,"individualLineStyles":[{"color":"#5470c6","width":2,"type":"solid","useGradient":false,"gradientColor":"#91cc75","showSymbol":true,"symbolSize":6,"symbolType":"circle","symbolColor":"#5470c6","symbolBorderColor":"#ffffff","symbolBorderWidth":0,"showArea":false,"areaOpacity":30,"showLabel":false,"labelPosition":"top","labelColor":"#333333","labelFontSize":12,"labelFontWeight":"normal"}],"barWidth":60,"borderRadius":0,"borderRadiusMode":"top","barColor":"#5470c6","useGradientBar":false,"barStartColor":"#5470c6","barEndColor":"#91cc75","gaugeMin":0,"gaugeMax":100,"gaugeUnit":"%","gaugeRadius":80,"pieType":"ring","pieRadius":70,"pieInnerRadius":40,"pieOuterRadius":70,"pieBorderRadius":0,"pieBorderRadiusMode":"all","showPieBorder":true,"pieShowDataLabels":true,"useCustomPieColors":false,"waterBorderWidth":5,"waterBorderColor":"#1890ff","useWaterBorderGradient":false,"waterBorderGradientColor":"#52c41a","waterWaveColor":"#1890ff","useWaterWaveGradient":false,"waterWaveGradientColor":"#52c41a","waterAmplitude":20,"showWaterLabel":true,"waterLabelFontSize":20,"waterLabelFontColor":"#333333","showPercentageSymbol":true,"columnBarWidth":40,"columnBorderRadius":8,"columnUseGradient":true,"columnStartColor":"#4facfe","columnEndColor":"#00f2fe","columnEnableShadow":true,"columnShadowColor":"#000000","columnShadowBlur":10,"columnShowDataLabel":true,"columnDataLabelPosition":"vertical-top","columnDataLabelColor":"#333333","columnDataLabelFontSize":14,"columnShowPercentage":true,"columnShowBarBackground":true,"columnBarBackgroundColor":"#f5f5f5","columnAllowOverflow":true,"textFontFamily":"Arial, sans-serif","textFontSize":16,"textFontColor":"#333333","textFontWeight":"normal","textFontStyle":"normal","textDecoration":"none","textAlign":"left","textVerticalAlign":"top","textLineHeight":1.5,"textLetterSpacing":0,"textPadding":10,"textBorderRadius":0,"enableTextGradient":false,"textGradientType":"linear","textGradientStartColor":"#ffffff","textGradientEndColor":"#f0f0f0","textGradientAngle":0,"imageUrl":"","imageBorderRadius":0,"imageRotation":0,"imageOpacity":100,"imageObjectFit":"contain","decorationObjectFit":"contain","decorationOpacity":100,"decorationRotation":0,"decorationBlendMode":"normal","tableStyle":"table","tableSize":"table-normal","showTableHeader":true,"tableHeaderBgColor":"#f8f9fa","tableHeaderTextColor":"#212529","tableBodyBgColor":"#ffffff","tableBodyTextColor":"#212529","tableBorderColor":"#dee2e6","tableHeaderFontSize":14,"tableBodyFontSize":12,"tableFontWeight":"normal","enableTableCarousel":false,"tableRowsPerPage":5,"tableCarouselInterval":3,"showTablePagination":false,"hyperlinkUrl":"","hyperlinkText":"点击跳转","hyperlinkTarget":"_self","hyperlinkFontSize":16,"hyperlinkFontColor":"#007bff","hyperlinkFontWeight":"normal","hyperlinkTextAlign":"left","hyperlinkFontStyle":"normal","hyperlinkTextDecoration":"none","hyperlinkOpacity":100,"htmlOpacity":100,"videoUrl":"","poster":"","autoplay":false,"loop":false,"muted":true,"controls":true,"opacity":100,"borderWidth":0,"shadow":false,"timeFormat":"YYYY-MM-DD HH:mm:ss","timeFontSize":24,"timeFontWeight":"normal","timeFontStyle":"normal","timeColorType":"solid","timeColor":"#333333","timeGradientStart":"#667eea","timeGradientEnd":"#764ba2","timeGradientDirection":"to right","timeOpacity":100,"timeTextShadow":false}
bi-dashboard-designer.js?v=20250731-export-debug:4674 新的styleConfig: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250731-export-debug:4683 更新后styleConfig字符串: {"theme":"default","backgroundColor":"#ffffff","borderColor":"#dee2e6","showTitle":true,"transparent":false,"titleFontSize":16,"showLegend":true,"showChartTitle":true,"borderlessMode":false,"showDataLabels":false,"showYAxis":true,"showYAxisLabels":true,"showXAxis":true,"yAxisFontSize":12,"yAxisFontColor":"#8c8c8c","xAxisFontSize":12,"xAxisFontColor":"#8c8c8c","dataLabelFontSize":11,"dataLabelFontColor":"#5470c6","legendFontSize":12,"legendFontColor":"#666666","lineWidth":3,"lineType":"solid","lineColor":"#5470c6","useGradientLine":false,"lineGradientColor":"#91cc75","smoothLine":true,"showArea":false,"useGradientArea":true,"areaStartColor":"#5470c6","areaMidColor":"#91cc75","areaEndColor":"#ffffff","showSymbol":true,"symbolType":"circle","symbolSize":8,"symbolColor":"#5470c6","enableShadow":false,"shadowBlur":10,"shadowOffsetY":3,"shadowColor":"#000000","multiLineSmooth":true,"multiLineShowSymbol":true,"multiLineSymbolSize":6,"multiLineWidth":2,"multiLineShowArea":false,"multiLineEnableDualYAxis":false,"useIndividualStyles":true,"lineCount":1,"individualLineStyles":[{"color":"#5470c6","width":2,"type":"solid","useGradient":false,"gradientColor":"#91cc75","showSymbol":true,"symbolSize":6,"symbolType":"circle","symbolColor":"#5470c6","symbolBorderColor":"#ffffff","symbolBorderWidth":0,"showArea":false,"areaOpacity":30,"showLabel":false,"labelPosition":"top","labelColor":"#333333","labelFontSize":12,"labelFontWeight":"normal"}],"barWidth":60,"borderRadius":0,"borderRadiusMode":"top","barColor":"#5470c6","useGradientBar":false,"barStartColor":"#5470c6","barEndColor":"#91cc75","gaugeMin":0,"gaugeMax":100,"gaugeUnit":"%","gaugeRadius":80,"pieType":"ring","pieRadius":70,"pieInnerRadius":40,"pieOuterRadius":70,"pieBorderRadius":0,"pieBorderRadiusMode":"all","showPieBorder":true,"pieShowDataLabels":true,"useCustomPieColors":false,"waterBorderWidth":5,"waterBorderColor":"#1890ff","useWaterBorderGradient":false,"waterBorderGradientColor":"#52c41a","waterWaveColor":"#1890ff","useWaterWaveGradient":false,"waterWaveGradientColor":"#52c41a","waterAmplitude":20,"showWaterLabel":true,"waterLabelFontSize":20,"waterLabelFontColor":"#333333","showPercentageSymbol":true,"columnBarWidth":40,"columnBorderRadius":8,"columnUseGradient":true,"columnStartColor":"#4facfe","columnEndColor":"#00f2fe","columnEnableShadow":true,"columnShadowColor":"#000000","columnShadowBlur":10,"columnShowDataLabel":true,"columnDataLabelPosition":"vertical-top","columnDataLabelColor":"#333333","columnDataLabelFontSize":14,"columnShowPercentage":true,"columnShowBarBackground":true,"columnBarBackgroundColor":"#f5f5f5","columnAllowOverflow":true,"textFontFamily":"Arial, sans-serif","textFontSize":16,"textFontColor":"#333333","textFontWeight":"normal","textFontStyle":"normal","textDecoration":"none","textAlign":"left","textVerticalAlign":"top","textLineHeight":1.5,"textLetterSpacing":0,"textPadding":10,"textBorderRadius":0,"enableTextGradient":false,"textGradientType":"linear","textGradientStartColor":"#ffffff","textGradientEndColor":"#f0f0f0","textGradientAngle":0,"imageUrl":"","imageBorderRadius":0,"imageRotation":0,"imageOpacity":100,"imageObjectFit":"contain","decorationObjectFit":"contain","decorationOpacity":100,"decorationRotation":0,"decorationBlendMode":"normal","tableStyle":"table","tableSize":"table-normal","showTableHeader":true,"tableHeaderBgColor":"#f8f9fa","tableHeaderTextColor":"#212529","tableBodyBgColor":"#ffffff","tableBodyTextColor":"#212529","tableBorderColor":"#dee2e6","tableHeaderFontSize":14,"tableBodyFontSize":12,"tableFontWeight":"normal","enableTableCarousel":false,"tableRowsPerPage":5,"tableCarouselInterval":3,"showTablePagination":false,"hyperlinkUrl":"","hyperlinkText":"点击跳转","hyperlinkTarget":"_self","hyperlinkFontSize":16,"hyperlinkFontColor":"#007bff","hyperlinkFontWeight":"normal","hyperlinkTextAlign":"left","hyperlinkFontStyle":"normal","hyperlinkTextDecoration":"none","hyperlinkOpacity":100,"htmlOpacity":100,"videoUrl":"","poster":"","autoplay":false,"loop":false,"muted":true,"controls":true,"opacity":100,"borderWidth":0,"shadow":false,"timeFormat":"YYYY-MM-DD HH:mm:ss","timeFontSize":24,"timeFontWeight":"normal","timeFontStyle":"normal","timeColorType":"solid","timeColor":"#333333","timeGradientStart":"#667eea","timeGradientEnd":"#764ba2","timeGradientDirection":"to right","timeOpacity":100,"timeTextShadow":false}
bi-dashboard-designer.js?v=20250731-export-debug:4684 === 样式配置更新完成 ===
bi-dashboard-designer.js?v=20250731-export-debug:11759 === 样式配置生命周期追踪：实时应用后 ===
bi-dashboard-designer.js?v=20250731-export-debug:11760 组件ID: 3440
bi-dashboard-designer.js?v=20250731-export-debug:11761 组件类型: data-table
bi-dashboard-designer.js?v=20250731-export-debug:11762 当前styleConfig: {"theme":"default","backgroundColor":"#ffffff","borderColor":"#dee2e6","showTitle":true,"transparent":false,"titleFontSize":16,"showLegend":true,"showChartTitle":true,"borderlessMode":false,"showDataLabels":false,"showYAxis":true,"showYAxisLabels":true,"showXAxis":true,"yAxisFontSize":12,"yAxisFontColor":"#8c8c8c","xAxisFontSize":12,"xAxisFontColor":"#8c8c8c","dataLabelFontSize":11,"dataLabelFontColor":"#5470c6","legendFontSize":12,"legendFontColor":"#666666","lineWidth":3,"lineType":"solid","lineColor":"#5470c6","useGradientLine":false,"lineGradientColor":"#91cc75","smoothLine":true,"showArea":false,"useGradientArea":true,"areaStartColor":"#5470c6","areaMidColor":"#91cc75","areaEndColor":"#ffffff","showSymbol":true,"symbolType":"circle","symbolSize":8,"symbolColor":"#5470c6","enableShadow":false,"shadowBlur":10,"shadowOffsetY":3,"shadowColor":"#000000","multiLineSmooth":true,"multiLineShowSymbol":true,"multiLineSymbolSize":6,"multiLineWidth":2,"multiLineShowArea":false,"multiLineEnableDualYAxis":false,"useIndividualStyles":true,"lineCount":1,"individualLineStyles":[{"color":"#5470c6","width":2,"type":"solid","useGradient":false,"gradientColor":"#91cc75","showSymbol":true,"symbolSize":6,"symbolType":"circle","symbolColor":"#5470c6","symbolBorderColor":"#ffffff","symbolBorderWidth":0,"showArea":false,"areaOpacity":30,"showLabel":false,"labelPosition":"top","labelColor":"#333333","labelFontSize":12,"labelFontWeight":"normal"}],"barWidth":60,"borderRadius":0,"borderRadiusMode":"top","barColor":"#5470c6","useGradientBar":false,"barStartColor":"#5470c6","barEndColor":"#91cc75","gaugeMin":0,"gaugeMax":100,"gaugeUnit":"%","gaugeRadius":80,"pieType":"ring","pieRadius":70,"pieInnerRadius":40,"pieOuterRadius":70,"pieBorderRadius":0,"pieBorderRadiusMode":"all","showPieBorder":true,"pieShowDataLabels":true,"useCustomPieColors":false,"waterBorderWidth":5,"waterBorderColor":"#1890ff","useWaterBorderGradient":false,"waterBorderGradientColor":"#52c41a","waterWaveColor":"#1890ff","useWaterWaveGradient":false,"waterWaveGradientColor":"#52c41a","waterAmplitude":20,"showWaterLabel":true,"waterLabelFontSize":20,"waterLabelFontColor":"#333333","showPercentageSymbol":true,"columnBarWidth":40,"columnBorderRadius":8,"columnUseGradient":true,"columnStartColor":"#4facfe","columnEndColor":"#00f2fe","columnEnableShadow":true,"columnShadowColor":"#000000","columnShadowBlur":10,"columnShowDataLabel":true,"columnDataLabelPosition":"vertical-top","columnDataLabelColor":"#333333","columnDataLabelFontSize":14,"columnShowPercentage":true,"columnShowBarBackground":true,"columnBarBackgroundColor":"#f5f5f5","columnAllowOverflow":true,"textFontFamily":"Arial, sans-serif","textFontSize":16,"textFontColor":"#333333","textFontWeight":"normal","textFontStyle":"normal","textDecoration":"none","textAlign":"left","textVerticalAlign":"top","textLineHeight":1.5,"textLetterSpacing":0,"textPadding":10,"textBorderRadius":0,"enableTextGradient":false,"textGradientType":"linear","textGradientStartColor":"#ffffff","textGradientEndColor":"#f0f0f0","textGradientAngle":0,"imageUrl":"","imageBorderRadius":0,"imageRotation":0,"imageOpacity":100,"imageObjectFit":"contain","decorationObjectFit":"contain","decorationOpacity":100,"decorationRotation":0,"decorationBlendMode":"normal","tableStyle":"table","tableSize":"table-normal","showTableHeader":true,"tableHeaderBgColor":"#f8f9fa","tableHeaderTextColor":"#212529","tableBodyBgColor":"#ffffff","tableBodyTextColor":"#212529","tableBorderColor":"#dee2e6","tableHeaderFontSize":14,"tableBodyFontSize":12,"tableFontWeight":"normal","enableTableCarousel":false,"tableRowsPerPage":5,"tableCarouselInterval":3,"showTablePagination":false,"hyperlinkUrl":"","hyperlinkText":"点击跳转","hyperlinkTarget":"_self","hyperlinkFontSize":16,"hyperlinkFontColor":"#007bff","hyperlinkFontWeight":"normal","hyperlinkTextAlign":"left","hyperlinkFontStyle":"normal","hyperlinkTextDecoration":"none","hyperlinkOpacity":100,"htmlOpacity":100,"videoUrl":"","poster":"","autoplay":false,"loop":false,"muted":true,"controls":true,"opacity":100,"borderWidth":0,"shadow":false,"timeFormat":"YYYY-MM-DD HH:mm:ss","timeFontSize":24,"timeFontWeight":"normal","timeFontStyle":"normal","timeColorType":"solid","timeColor":"#333333","timeGradientStart":"#667eea","timeGradientEnd":"#764ba2","timeGradientDirection":"to right","timeOpacity":100,"timeTextShadow":false}
bi-dashboard-designer.js?v=20250731-export-debug:11767 解析后的样式配置: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250731-export-debug:11768 配置项数量: 169
bi-dashboard-designer.js?v=20250731-export-debug:11769 重要配置检查: {showDataLabels: false, primaryColor: undefined, theme: 'default'}
bi-dashboard-designer.js?v=20250731-export-debug:11782 附加信息: {formConfigKeys: Array(169), hasFormConfig: true}
bi-dashboard-designer.js?v=20250731-export-debug:11784 === 追踪结束 ===
bi-dashboard-designer.js?v=20250731-export-debug:4793 表格组件配置更新，重新加载数据和样式
bi-dashboard-designer.js?v=20250731-export-debug:10030 更新组件数据: 3440 data-table
bi-data-source-manager.js?v=20250127:913 === BiDataSourceManager - 开始获取组件数据 ===
bi-data-source-manager.js?v=20250127:914 组件信息: {id: 3440, type: 'data-table', dataSourceConfig: '{"dataSourceType":"externalData","multiDataSet":tr…nTargetDataItem":"","showColumnTargetLabel":true}'}
bi-data-source-manager.js?v=20250127:959 解析字符串格式数据源配置: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(1), refreshInterval: 5, mergeStrategy: 'union', …}
bi-data-source-manager.js?v=20250127:974 确定的数据源类型: externalData
bi-data-source-manager.js?v=20250127:975 完整数据源配置: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(1), refreshInterval: 5, mergeStrategy: 'union', …}
bi-data-source-manager.js?v=20250127:990 处理外部数据源...
bi-data-source-manager.js?v=20250127:2198 处理多外部数据源数据: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(1), refreshInterval: 5, mergeStrategy: 'union', …}
bi-data-source-manager.js?v=20250127:2298 处理多外部数据源，数据集数量: 1
bi-data-source-manager.js?v=20250127:2306 获取数据集 1 (实际索引: 0): {dataSetId: 'dataset_1749946359215', dataSetName: 'ttt1', alias: '数据集1', domIndex: 0, tableFields: Array(0)}
bi-dashboard-designer.js?v=20250731-export-debug:12752 组件 3440 自动刷新条件检查: {dataSourceType: 'externalData', hasDataItemId: false, hasDataSetId: false, isMultiDataSet: true, dataSetsCount: 1, …}
bi-dashboard-designer.js?v=20250731-export-debug:12768 设置组件 3440 自动刷新间隔: 5000ms (数据源类型: externalData)
bi-dashboard-designer.js?v=20250731-export-debug:4836 实时应用属性: {id: 3440, type: 'data-table', x: 116, y: 318, width: 300, …}
bi-data-source-manager.js?v=20250127:2314   POST http://localhost:8080/api/bi/dataset/dataset_1749946359215/data 500 (Internal Server Error)
(匿名) @ bi-data-source-manager.js?v=20250127:2314
processMultiExternalData @ bi-data-source-manager.js?v=20250127:2304
processExternalData @ bi-data-source-manager.js?v=20250127:2199
fetchWidgetData @ bi-data-source-manager.js?v=20250127:991
updateWidgetData @ bi-dashboard-designer.js?v=20250731-export-debug:10035
applyPropertiesRealTime @ bi-dashboard-designer.js?v=20250731-export-debug:4811
onchange @ design:1
handleMouseUp_ @ 未知
bi-data-source-manager.js?v=20250127:2354 所有数据集查询完成: [{…}]
bi-data-source-manager.js?v=20250127:2712 合并多外部数据源数据: {results: Array(1), mergeStrategy: 'union', componentType: 'data-table'}
bi-data-source-manager.js?v=20250127:1000 数据获取结果: {success: false, message: '所有数据集查询失败: 数据集1: 服务器错误', labels: Array(1), values: Array(1), data: Array(1)}
bi-data-source-manager.js?v=20250127:1001 === BiDataSourceManager - 数据获取完成 ===
bi-dashboard-designer.js?v=20250731-export-debug:10044  数据获取失败: undefined
updateWidgetData @ bi-dashboard-designer.js?v=20250731-export-debug:10044
await in updateWidgetData
applyPropertiesRealTime @ bi-dashboard-designer.js?v=20250731-export-debug:4811
onchange @ design:1
handleMouseUp_ @ 未知
bi-dashboard-designer.js?v=20250731-export-debug:12786 组件 3440 开始首次数据更新，数据源类型: externalData
bi-dashboard-designer.js?v=20250731-export-debug:12860 安全更新组件数据: 3440 data-table
bi-dashboard-designer.js?v=20250731-export-debug:10030 更新组件数据: 3440 data-table
bi-data-source-manager.js?v=20250127:913 === BiDataSourceManager - 开始获取组件数据 ===
bi-data-source-manager.js?v=20250127:914 组件信息: {id: 3440, type: 'data-table', dataSourceConfig: '{"dataSourceType":"externalData","multiDataSet":tr…nTargetDataItem":"","showColumnTargetLabel":true}'}
bi-data-source-manager.js?v=20250127:959 解析字符串格式数据源配置: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(1), refreshInterval: 5, mergeStrategy: 'union', …}
bi-data-source-manager.js?v=20250127:974 确定的数据源类型: externalData
bi-data-source-manager.js?v=20250127:975 完整数据源配置: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(1), refreshInterval: 5, mergeStrategy: 'union', …}
bi-data-source-manager.js?v=20250127:990 处理外部数据源...
bi-data-source-manager.js?v=20250127:2198 处理多外部数据源数据: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(1), refreshInterval: 5, mergeStrategy: 'union', …}
bi-data-source-manager.js?v=20250127:2298 处理多外部数据源，数据集数量: 1
bi-data-source-manager.js?v=20250127:2306 获取数据集 1 (实际索引: 0): {dataSetId: 'dataset_1749946359215', dataSetName: 'ttt1', alias: '数据集1', domIndex: 0, tableFields: Array(0)}
bi-dashboard-designer.js?v=20250731-export-debug:12788 组件 3440 首次数据更新完成
bi-data-source-manager.js?v=20250127:2314   POST http://localhost:8080/api/bi/dataset/dataset_1749946359215/data 500 (Internal Server Error)
(匿名) @ bi-data-source-manager.js?v=20250127:2314
processMultiExternalData @ bi-data-source-manager.js?v=20250127:2304
processExternalData @ bi-data-source-manager.js?v=20250127:2199
fetchWidgetData @ bi-data-source-manager.js?v=20250127:991
updateWidgetData @ bi-dashboard-designer.js?v=20250731-export-debug:10035
updateWidgetDataSafe @ bi-dashboard-designer.js?v=20250731-export-debug:12871
waitForChartInit @ bi-dashboard-designer.js?v=20250731-export-debug:12787
setTimeout
setupWidgetAutoRefresh @ bi-dashboard-designer.js?v=20250731-export-debug:12792
applyPropertiesRealTime @ bi-dashboard-designer.js?v=20250731-export-debug:4824
onchange @ design:1
handleMouseUp_ @ 未知
bi-data-source-manager.js?v=20250127:2354 所有数据集查询完成: [{…}]
bi-data-source-manager.js?v=20250127:2712 合并多外部数据源数据: {results: Array(1), mergeStrategy: 'union', componentType: 'data-table'}
bi-data-source-manager.js?v=20250127:1000 数据获取结果: {success: false, message: '所有数据集查询失败: 数据集1: 服务器错误', labels: Array(1), values: Array(1), data: Array(1)}
bi-data-source-manager.js?v=20250127:1001 === BiDataSourceManager - 数据获取完成 ===
bi-dashboard-designer.js?v=20250731-export-debug:10044  数据获取失败: undefined
updateWidgetData @ bi-dashboard-designer.js?v=20250731-export-debug:10044
await in updateWidgetData
updateWidgetDataSafe @ bi-dashboard-designer.js?v=20250731-export-debug:12871
waitForChartInit @ bi-dashboard-designer.js?v=20250731-export-debug:12787
setTimeout
setupWidgetAutoRefresh @ bi-dashboard-designer.js?v=20250731-export-debug:12792
applyPropertiesRealTime @ bi-dashboard-designer.js?v=20250731-export-debug:4824
onchange @ design:1
handleMouseUp_ @ 未知
bi-dashboard-designer.js?v=20250731-export-debug:464 组件 3440 (data-table) 已标记为修改: properties
bi-dashboard-designer.js?v=20250731-export-debug:4042 缓存组件 3440 配置，时间戳: 2025-08-01T06:09:07.930Z
bi-dashboard-designer.js?v=20250731-export-debug:532 同步组件 3440 配置缓存
bi-data-source-manager.js?v=20250127:207 已清理组件 3440 的配置上下文
bi-data-source-manager.js?v=20250127:179 为组件 3440 (data-table) 创建配置上下文
bi-data-source-manager.js?v=20250127:249 为组件 3440 缓存DOM元素: dataSourceType
bi-data-source-manager.js?v=20250127:1451 收集组件 3440 (data-table) 外部数据源配置，多数据集模式: true
bi-data-source-manager.js?v=20250127:1513 收集多数据集配置 - 容器ID: tableExternalDataSourceList
bi-data-source-manager.js?v=20250127:1514 收集多数据集配置 - 找到数据集项数量: 1
bi-data-source-manager.js?v=20250127:1523 数据集项 DOM索引0: {itemId: 'externalDataSet_0', dataSetId: 'dataset_1749946359215', dataSetName: 'ttt1', labelField: 'null', valueField: 'null', …}
bi-data-source-manager.js?v=20250127:1645 collectTableFieldConfigFromContainer: 字段卡片 1: {cardId: 'tableField_dataSet_0_field_0', nameInput: 'tableFieldName_dataSet_0_field_0', mappingSelect: 'tableFieldMapping_dataSet_0_field_0', nameValue: '2323', mappingValue: 'latest_value'}
bi-data-source-manager.js?v=20250127:1659 collectTableFieldConfigFromContainer: 添加字段配置: {displayName: '2323', dataField: 'latest_value'}
bi-data-source-manager.js?v=20250127:1564 添加数据集配置 DOM索引0: {dataSetId: 'dataset_1749946359215', dataSetName: 'ttt1', alias: '数据集1', domIndex: 0, tableFields: Array(1)}
bi-data-source-manager.js?v=20250127:1571 最终收集到的数据集配置: [{…}]
bi-data-source-manager.js?v=20250127:1456 组件 3440 收集到多数据集配置，数据集数量: 1
bi-data-source-manager.js?v=20250127:429 组件 3440 数据源配置收集完成: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(1), refreshInterval: 5, mergeStrategy: 'union'}
bi-dashboard-designer.js?v=20250731-export-debug:11789 === 开始收集样式配置 ===
bi-dashboard-designer.js?v=20250731-export-debug:11405 🔧 [修复版本 v2.0] 开始收集多折线样式配置
bi-dashboard-designer.js?v=20250731-export-debug:11412 🔧 [修复版本] 折线数量: 1 (来源: 实际数据 )
bi-dashboard-designer.js?v=20250731-export-debug:11423 收集折线 1 的样式配置
bi-dashboard-designer.js?v=20250731-export-debug:11457 收集到的多折线样式配置: [{…}]
bi-dashboard-designer.js?v=20250731-export-debug:12484 === 样式配置收集完成 ===
bi-dashboard-designer.js?v=20250731-export-debug:12485 收集到的配置项数量: 169
bi-dashboard-designer.js?v=20250731-export-debug:12486 重要配置项检查: {showDataLabels: false, primaryColor: undefined, theme: 'default', showChartTitle: true, showLegend: true, …}
bi-dashboard-designer.js?v=20250731-export-debug:12502 完整样式配置: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250731-export-debug:4670 === 实时应用属性：样式配置更新 ===
bi-dashboard-designer.js?v=20250731-export-debug:4671 组件ID: 3440
bi-dashboard-designer.js?v=20250731-export-debug:4672 组件类型: data-table
bi-dashboard-designer.js?v=20250731-export-debug:4673 更新前styleConfig: {"theme":"default","backgroundColor":"#ffffff","borderColor":"#dee2e6","showTitle":true,"transparent":false,"titleFontSize":16,"showLegend":true,"showChartTitle":true,"borderlessMode":false,"showDataLabels":false,"showYAxis":true,"showYAxisLabels":true,"showXAxis":true,"yAxisFontSize":12,"yAxisFontColor":"#8c8c8c","xAxisFontSize":12,"xAxisFontColor":"#8c8c8c","dataLabelFontSize":11,"dataLabelFontColor":"#5470c6","legendFontSize":12,"legendFontColor":"#666666","lineWidth":3,"lineType":"solid","lineColor":"#5470c6","useGradientLine":false,"lineGradientColor":"#91cc75","smoothLine":true,"showArea":false,"useGradientArea":true,"areaStartColor":"#5470c6","areaMidColor":"#91cc75","areaEndColor":"#ffffff","showSymbol":true,"symbolType":"circle","symbolSize":8,"symbolColor":"#5470c6","enableShadow":false,"shadowBlur":10,"shadowOffsetY":3,"shadowColor":"#000000","multiLineSmooth":true,"multiLineShowSymbol":true,"multiLineSymbolSize":6,"multiLineWidth":2,"multiLineShowArea":false,"multiLineEnableDualYAxis":false,"useIndividualStyles":true,"lineCount":1,"individualLineStyles":[{"color":"#5470c6","width":2,"type":"solid","useGradient":false,"gradientColor":"#91cc75","showSymbol":true,"symbolSize":6,"symbolType":"circle","symbolColor":"#5470c6","symbolBorderColor":"#ffffff","symbolBorderWidth":0,"showArea":false,"areaOpacity":30,"showLabel":false,"labelPosition":"top","labelColor":"#333333","labelFontSize":12,"labelFontWeight":"normal"}],"barWidth":60,"borderRadius":0,"borderRadiusMode":"top","barColor":"#5470c6","useGradientBar":false,"barStartColor":"#5470c6","barEndColor":"#91cc75","gaugeMin":0,"gaugeMax":100,"gaugeUnit":"%","gaugeRadius":80,"pieType":"ring","pieRadius":70,"pieInnerRadius":40,"pieOuterRadius":70,"pieBorderRadius":0,"pieBorderRadiusMode":"all","showPieBorder":true,"pieShowDataLabels":true,"useCustomPieColors":false,"waterBorderWidth":5,"waterBorderColor":"#1890ff","useWaterBorderGradient":false,"waterBorderGradientColor":"#52c41a","waterWaveColor":"#1890ff","useWaterWaveGradient":false,"waterWaveGradientColor":"#52c41a","waterAmplitude":20,"showWaterLabel":true,"waterLabelFontSize":20,"waterLabelFontColor":"#333333","showPercentageSymbol":true,"columnBarWidth":40,"columnBorderRadius":8,"columnUseGradient":true,"columnStartColor":"#4facfe","columnEndColor":"#00f2fe","columnEnableShadow":true,"columnShadowColor":"#000000","columnShadowBlur":10,"columnShowDataLabel":true,"columnDataLabelPosition":"vertical-top","columnDataLabelColor":"#333333","columnDataLabelFontSize":14,"columnShowPercentage":true,"columnShowBarBackground":true,"columnBarBackgroundColor":"#f5f5f5","columnAllowOverflow":true,"textFontFamily":"Arial, sans-serif","textFontSize":16,"textFontColor":"#333333","textFontWeight":"normal","textFontStyle":"normal","textDecoration":"none","textAlign":"left","textVerticalAlign":"top","textLineHeight":1.5,"textLetterSpacing":0,"textPadding":10,"textBorderRadius":0,"enableTextGradient":false,"textGradientType":"linear","textGradientStartColor":"#ffffff","textGradientEndColor":"#f0f0f0","textGradientAngle":0,"imageUrl":"","imageBorderRadius":0,"imageRotation":0,"imageOpacity":100,"imageObjectFit":"contain","decorationObjectFit":"contain","decorationOpacity":100,"decorationRotation":0,"decorationBlendMode":"normal","tableStyle":"table","tableSize":"table-normal","showTableHeader":true,"tableHeaderBgColor":"#f8f9fa","tableHeaderTextColor":"#212529","tableBodyBgColor":"#ffffff","tableBodyTextColor":"#212529","tableBorderColor":"#dee2e6","tableHeaderFontSize":14,"tableBodyFontSize":12,"tableFontWeight":"normal","enableTableCarousel":false,"tableRowsPerPage":5,"tableCarouselInterval":3,"showTablePagination":false,"hyperlinkUrl":"","hyperlinkText":"点击跳转","hyperlinkTarget":"_self","hyperlinkFontSize":16,"hyperlinkFontColor":"#007bff","hyperlinkFontWeight":"normal","hyperlinkTextAlign":"left","hyperlinkFontStyle":"normal","hyperlinkTextDecoration":"none","hyperlinkOpacity":100,"htmlOpacity":100,"videoUrl":"","poster":"","autoplay":false,"loop":false,"muted":true,"controls":true,"opacity":100,"borderWidth":0,"shadow":false,"timeFormat":"YYYY-MM-DD HH:mm:ss","timeFontSize":24,"timeFontWeight":"normal","timeFontStyle":"normal","timeColorType":"solid","timeColor":"#333333","timeGradientStart":"#667eea","timeGradientEnd":"#764ba2","timeGradientDirection":"to right","timeOpacity":100,"timeTextShadow":false}
bi-dashboard-designer.js?v=20250731-export-debug:4674 新的styleConfig: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250731-export-debug:4683 更新后styleConfig字符串: {"theme":"default","backgroundColor":"#ffffff","borderColor":"#dee2e6","showTitle":true,"transparent":false,"titleFontSize":16,"showLegend":true,"showChartTitle":true,"borderlessMode":false,"showDataLabels":false,"showYAxis":true,"showYAxisLabels":true,"showXAxis":true,"yAxisFontSize":12,"yAxisFontColor":"#8c8c8c","xAxisFontSize":12,"xAxisFontColor":"#8c8c8c","dataLabelFontSize":11,"dataLabelFontColor":"#5470c6","legendFontSize":12,"legendFontColor":"#666666","lineWidth":3,"lineType":"solid","lineColor":"#5470c6","useGradientLine":false,"lineGradientColor":"#91cc75","smoothLine":true,"showArea":false,"useGradientArea":true,"areaStartColor":"#5470c6","areaMidColor":"#91cc75","areaEndColor":"#ffffff","showSymbol":true,"symbolType":"circle","symbolSize":8,"symbolColor":"#5470c6","enableShadow":false,"shadowBlur":10,"shadowOffsetY":3,"shadowColor":"#000000","multiLineSmooth":true,"multiLineShowSymbol":true,"multiLineSymbolSize":6,"multiLineWidth":2,"multiLineShowArea":false,"multiLineEnableDualYAxis":false,"useIndividualStyles":true,"lineCount":1,"individualLineStyles":[{"color":"#5470c6","width":2,"type":"solid","useGradient":false,"gradientColor":"#91cc75","showSymbol":true,"symbolSize":6,"symbolType":"circle","symbolColor":"#5470c6","symbolBorderColor":"#ffffff","symbolBorderWidth":0,"showArea":false,"areaOpacity":30,"showLabel":false,"labelPosition":"top","labelColor":"#333333","labelFontSize":12,"labelFontWeight":"normal"}],"barWidth":60,"borderRadius":0,"borderRadiusMode":"top","barColor":"#5470c6","useGradientBar":false,"barStartColor":"#5470c6","barEndColor":"#91cc75","gaugeMin":0,"gaugeMax":100,"gaugeUnit":"%","gaugeRadius":80,"pieType":"ring","pieRadius":70,"pieInnerRadius":40,"pieOuterRadius":70,"pieBorderRadius":0,"pieBorderRadiusMode":"all","showPieBorder":true,"pieShowDataLabels":true,"useCustomPieColors":false,"waterBorderWidth":5,"waterBorderColor":"#1890ff","useWaterBorderGradient":false,"waterBorderGradientColor":"#52c41a","waterWaveColor":"#1890ff","useWaterWaveGradient":false,"waterWaveGradientColor":"#52c41a","waterAmplitude":20,"showWaterLabel":true,"waterLabelFontSize":20,"waterLabelFontColor":"#333333","showPercentageSymbol":true,"columnBarWidth":40,"columnBorderRadius":8,"columnUseGradient":true,"columnStartColor":"#4facfe","columnEndColor":"#00f2fe","columnEnableShadow":true,"columnShadowColor":"#000000","columnShadowBlur":10,"columnShowDataLabel":true,"columnDataLabelPosition":"vertical-top","columnDataLabelColor":"#333333","columnDataLabelFontSize":14,"columnShowPercentage":true,"columnShowBarBackground":true,"columnBarBackgroundColor":"#f5f5f5","columnAllowOverflow":true,"textFontFamily":"Arial, sans-serif","textFontSize":16,"textFontColor":"#333333","textFontWeight":"normal","textFontStyle":"normal","textDecoration":"none","textAlign":"left","textVerticalAlign":"top","textLineHeight":1.5,"textLetterSpacing":0,"textPadding":10,"textBorderRadius":0,"enableTextGradient":false,"textGradientType":"linear","textGradientStartColor":"#ffffff","textGradientEndColor":"#f0f0f0","textGradientAngle":0,"imageUrl":"","imageBorderRadius":0,"imageRotation":0,"imageOpacity":100,"imageObjectFit":"contain","decorationObjectFit":"contain","decorationOpacity":100,"decorationRotation":0,"decorationBlendMode":"normal","tableStyle":"table","tableSize":"table-normal","showTableHeader":true,"tableHeaderBgColor":"#f8f9fa","tableHeaderTextColor":"#212529","tableBodyBgColor":"#ffffff","tableBodyTextColor":"#212529","tableBorderColor":"#dee2e6","tableHeaderFontSize":14,"tableBodyFontSize":12,"tableFontWeight":"normal","enableTableCarousel":false,"tableRowsPerPage":5,"tableCarouselInterval":3,"showTablePagination":false,"hyperlinkUrl":"","hyperlinkText":"点击跳转","hyperlinkTarget":"_self","hyperlinkFontSize":16,"hyperlinkFontColor":"#007bff","hyperlinkFontWeight":"normal","hyperlinkTextAlign":"left","hyperlinkFontStyle":"normal","hyperlinkTextDecoration":"none","hyperlinkOpacity":100,"htmlOpacity":100,"videoUrl":"","poster":"","autoplay":false,"loop":false,"muted":true,"controls":true,"opacity":100,"borderWidth":0,"shadow":false,"timeFormat":"YYYY-MM-DD HH:mm:ss","timeFontSize":24,"timeFontWeight":"normal","timeFontStyle":"normal","timeColorType":"solid","timeColor":"#333333","timeGradientStart":"#667eea","timeGradientEnd":"#764ba2","timeGradientDirection":"to right","timeOpacity":100,"timeTextShadow":false}
bi-dashboard-designer.js?v=20250731-export-debug:4684 === 样式配置更新完成 ===
bi-dashboard-designer.js?v=20250731-export-debug:11759 === 样式配置生命周期追踪：实时应用后 ===
bi-dashboard-designer.js?v=20250731-export-debug:11760 组件ID: 3440
bi-dashboard-designer.js?v=20250731-export-debug:11761 组件类型: data-table
bi-dashboard-designer.js?v=20250731-export-debug:11762 当前styleConfig: {"theme":"default","backgroundColor":"#ffffff","borderColor":"#dee2e6","showTitle":true,"transparent":false,"titleFontSize":16,"showLegend":true,"showChartTitle":true,"borderlessMode":false,"showDataLabels":false,"showYAxis":true,"showYAxisLabels":true,"showXAxis":true,"yAxisFontSize":12,"yAxisFontColor":"#8c8c8c","xAxisFontSize":12,"xAxisFontColor":"#8c8c8c","dataLabelFontSize":11,"dataLabelFontColor":"#5470c6","legendFontSize":12,"legendFontColor":"#666666","lineWidth":3,"lineType":"solid","lineColor":"#5470c6","useGradientLine":false,"lineGradientColor":"#91cc75","smoothLine":true,"showArea":false,"useGradientArea":true,"areaStartColor":"#5470c6","areaMidColor":"#91cc75","areaEndColor":"#ffffff","showSymbol":true,"symbolType":"circle","symbolSize":8,"symbolColor":"#5470c6","enableShadow":false,"shadowBlur":10,"shadowOffsetY":3,"shadowColor":"#000000","multiLineSmooth":true,"multiLineShowSymbol":true,"multiLineSymbolSize":6,"multiLineWidth":2,"multiLineShowArea":false,"multiLineEnableDualYAxis":false,"useIndividualStyles":true,"lineCount":1,"individualLineStyles":[{"color":"#5470c6","width":2,"type":"solid","useGradient":false,"gradientColor":"#91cc75","showSymbol":true,"symbolSize":6,"symbolType":"circle","symbolColor":"#5470c6","symbolBorderColor":"#ffffff","symbolBorderWidth":0,"showArea":false,"areaOpacity":30,"showLabel":false,"labelPosition":"top","labelColor":"#333333","labelFontSize":12,"labelFontWeight":"normal"}],"barWidth":60,"borderRadius":0,"borderRadiusMode":"top","barColor":"#5470c6","useGradientBar":false,"barStartColor":"#5470c6","barEndColor":"#91cc75","gaugeMin":0,"gaugeMax":100,"gaugeUnit":"%","gaugeRadius":80,"pieType":"ring","pieRadius":70,"pieInnerRadius":40,"pieOuterRadius":70,"pieBorderRadius":0,"pieBorderRadiusMode":"all","showPieBorder":true,"pieShowDataLabels":true,"useCustomPieColors":false,"waterBorderWidth":5,"waterBorderColor":"#1890ff","useWaterBorderGradient":false,"waterBorderGradientColor":"#52c41a","waterWaveColor":"#1890ff","useWaterWaveGradient":false,"waterWaveGradientColor":"#52c41a","waterAmplitude":20,"showWaterLabel":true,"waterLabelFontSize":20,"waterLabelFontColor":"#333333","showPercentageSymbol":true,"columnBarWidth":40,"columnBorderRadius":8,"columnUseGradient":true,"columnStartColor":"#4facfe","columnEndColor":"#00f2fe","columnEnableShadow":true,"columnShadowColor":"#000000","columnShadowBlur":10,"columnShowDataLabel":true,"columnDataLabelPosition":"vertical-top","columnDataLabelColor":"#333333","columnDataLabelFontSize":14,"columnShowPercentage":true,"columnShowBarBackground":true,"columnBarBackgroundColor":"#f5f5f5","columnAllowOverflow":true,"textFontFamily":"Arial, sans-serif","textFontSize":16,"textFontColor":"#333333","textFontWeight":"normal","textFontStyle":"normal","textDecoration":"none","textAlign":"left","textVerticalAlign":"top","textLineHeight":1.5,"textLetterSpacing":0,"textPadding":10,"textBorderRadius":0,"enableTextGradient":false,"textGradientType":"linear","textGradientStartColor":"#ffffff","textGradientEndColor":"#f0f0f0","textGradientAngle":0,"imageUrl":"","imageBorderRadius":0,"imageRotation":0,"imageOpacity":100,"imageObjectFit":"contain","decorationObjectFit":"contain","decorationOpacity":100,"decorationRotation":0,"decorationBlendMode":"normal","tableStyle":"table","tableSize":"table-normal","showTableHeader":true,"tableHeaderBgColor":"#f8f9fa","tableHeaderTextColor":"#212529","tableBodyBgColor":"#ffffff","tableBodyTextColor":"#212529","tableBorderColor":"#dee2e6","tableHeaderFontSize":14,"tableBodyFontSize":12,"tableFontWeight":"normal","enableTableCarousel":false,"tableRowsPerPage":5,"tableCarouselInterval":3,"showTablePagination":false,"hyperlinkUrl":"","hyperlinkText":"点击跳转","hyperlinkTarget":"_self","hyperlinkFontSize":16,"hyperlinkFontColor":"#007bff","hyperlinkFontWeight":"normal","hyperlinkTextAlign":"left","hyperlinkFontStyle":"normal","hyperlinkTextDecoration":"none","hyperlinkOpacity":100,"htmlOpacity":100,"videoUrl":"","poster":"","autoplay":false,"loop":false,"muted":true,"controls":true,"opacity":100,"borderWidth":0,"shadow":false,"timeFormat":"YYYY-MM-DD HH:mm:ss","timeFontSize":24,"timeFontWeight":"normal","timeFontStyle":"normal","timeColorType":"solid","timeColor":"#333333","timeGradientStart":"#667eea","timeGradientEnd":"#764ba2","timeGradientDirection":"to right","timeOpacity":100,"timeTextShadow":false}
bi-dashboard-designer.js?v=20250731-export-debug:11767 解析后的样式配置: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250731-export-debug:11768 配置项数量: 169
bi-dashboard-designer.js?v=20250731-export-debug:11769 重要配置检查: {showDataLabels: false, primaryColor: undefined, theme: 'default'}
bi-dashboard-designer.js?v=20250731-export-debug:11782 附加信息: {formConfigKeys: Array(169), hasFormConfig: true}
bi-dashboard-designer.js?v=20250731-export-debug:11784 === 追踪结束 ===
bi-dashboard-designer.js?v=20250731-export-debug:4793 表格组件配置更新，重新加载数据和样式
bi-dashboard-designer.js?v=20250731-export-debug:10030 更新组件数据: 3440 data-table
bi-data-source-manager.js?v=20250127:913 === BiDataSourceManager - 开始获取组件数据 ===
bi-data-source-manager.js?v=20250127:914 组件信息: {id: 3440, type: 'data-table', dataSourceConfig: '{"dataSourceType":"externalData","multiDataSet":tr…nTargetDataItem":"","showColumnTargetLabel":true}'}
bi-data-source-manager.js?v=20250127:959 解析字符串格式数据源配置: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(1), refreshInterval: 5, mergeStrategy: 'union', …}
bi-data-source-manager.js?v=20250127:974 确定的数据源类型: externalData
bi-data-source-manager.js?v=20250127:975 完整数据源配置: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(1), refreshInterval: 5, mergeStrategy: 'union', …}
bi-data-source-manager.js?v=20250127:990 处理外部数据源...
bi-data-source-manager.js?v=20250127:2198 处理多外部数据源数据: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(1), refreshInterval: 5, mergeStrategy: 'union', …}
bi-data-source-manager.js?v=20250127:2298 处理多外部数据源，数据集数量: 1
bi-data-source-manager.js?v=20250127:2306 获取数据集 1 (实际索引: 0): {dataSetId: 'dataset_1749946359215', dataSetName: 'ttt1', alias: '数据集1', domIndex: 0, tableFields: Array(1)}
bi-dashboard-designer.js?v=20250731-export-debug:12752 组件 3440 自动刷新条件检查: {dataSourceType: 'externalData', hasDataItemId: false, hasDataSetId: false, isMultiDataSet: true, dataSetsCount: 1, …}
bi-dashboard-designer.js?v=20250731-export-debug:12768 设置组件 3440 自动刷新间隔: 5000ms (数据源类型: externalData)
bi-dashboard-designer.js?v=20250731-export-debug:4836 实时应用属性: {id: 3440, type: 'data-table', x: 116, y: 318, width: 300, …}
bi-data-source-manager.js?v=20250127:2314   POST http://localhost:8080/api/bi/dataset/dataset_1749946359215/data 500 (Internal Server Error)
(匿名) @ bi-data-source-manager.js?v=20250127:2314
processMultiExternalData @ bi-data-source-manager.js?v=20250127:2304
processExternalData @ bi-data-source-manager.js?v=20250127:2199
fetchWidgetData @ bi-data-source-manager.js?v=20250127:991
updateWidgetData @ bi-dashboard-designer.js?v=20250731-export-debug:10035
applyPropertiesRealTime @ bi-dashboard-designer.js?v=20250731-export-debug:4811
onchange @ design:1
bi-data-source-manager.js?v=20250127:2354 所有数据集查询完成: [{…}]
bi-data-source-manager.js?v=20250127:2712 合并多外部数据源数据: {results: Array(1), mergeStrategy: 'union', componentType: 'data-table'}
bi-data-source-manager.js?v=20250127:1000 数据获取结果: {success: false, message: '所有数据集查询失败: 数据集1: 服务器错误', labels: Array(1), values: Array(1), data: Array(1)}
bi-data-source-manager.js?v=20250127:1001 === BiDataSourceManager - 数据获取完成 ===
bi-dashboard-designer.js?v=20250731-export-debug:10044  数据获取失败: undefined
updateWidgetData @ bi-dashboard-designer.js?v=20250731-export-debug:10044
await in updateWidgetData
applyPropertiesRealTime @ bi-dashboard-designer.js?v=20250731-export-debug:4811
onchange @ design:1
bi-dashboard-designer.js?v=20250731-export-debug:12786 组件 3440 开始首次数据更新，数据源类型: externalData
bi-dashboard-designer.js?v=20250731-export-debug:12860 安全更新组件数据: 3440 data-table
bi-dashboard-designer.js?v=20250731-export-debug:10030 更新组件数据: 3440 data-table
bi-data-source-manager.js?v=20250127:913 === BiDataSourceManager - 开始获取组件数据 ===
bi-data-source-manager.js?v=20250127:914 组件信息: {id: 3440, type: 'data-table', dataSourceConfig: '{"dataSourceType":"externalData","multiDataSet":tr…nTargetDataItem":"","showColumnTargetLabel":true}'}
bi-data-source-manager.js?v=20250127:959 解析字符串格式数据源配置: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(1), refreshInterval: 5, mergeStrategy: 'union', …}
bi-data-source-manager.js?v=20250127:974 确定的数据源类型: externalData
bi-data-source-manager.js?v=20250127:975 完整数据源配置: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(1), refreshInterval: 5, mergeStrategy: 'union', …}
bi-data-source-manager.js?v=20250127:990 处理外部数据源...
bi-data-source-manager.js?v=20250127:2198 处理多外部数据源数据: {dataSourceType: 'externalData', multiDataSet: true, dataSets: Array(1), refreshInterval: 5, mergeStrategy: 'union', …}
bi-data-source-manager.js?v=20250127:2298 处理多外部数据源，数据集数量: 1
bi-data-source-manager.js?v=20250127:2306 获取数据集 1 (实际索引: 0): {dataSetId: 'dataset_1749946359215', dataSetName: 'ttt1', alias: '数据集1', domIndex: 0, tableFields: Array(1)}
bi-dashboard-designer.js?v=20250731-export-debug:12788 组件 3440 首次数据更新完成
bi-data-source-manager.js?v=20250127:2314   POST http://localhost:8080/api/bi/dataset/dataset_1749946359215/data 500 (Internal Server Error)
(匿名) @ bi-data-source-manager.js?v=20250127:2314
processMultiExternalData @ bi-data-source-manager.js?v=20250127:2304
processExternalData @ bi-data-source-manager.js?v=20250127:2199
fetchWidgetData @ bi-data-source-manager.js?v=20250127:991
updateWidgetData @ bi-dashboard-designer.js?v=20250731-export-debug:10035
updateWidgetDataSafe @ bi-dashboard-designer.js?v=20250731-export-debug:12871
waitForChartInit @ bi-dashboard-designer.js?v=20250731-export-debug:12787
setTimeout
setupWidgetAutoRefresh @ bi-dashboard-designer.js?v=20250731-export-debug:12792
applyPropertiesRealTime @ bi-dashboard-designer.js?v=20250731-export-debug:4824
onchange @ design:1
bi-data-source-manager.js?v=20250127:2354 所有数据集查询完成: [{…}]
bi-data-source-manager.js?v=20250127:2712 合并多外部数据源数据: {results: Array(1), mergeStrategy: 'union', componentType: 'data-table'}
bi-data-source-manager.js?v=20250127:1000 数据获取结果: {success: false, message: '所有数据集查询失败: 数据集1: 服务器错误', labels: Array(1), values: Array(1), data: Array(1)}
bi-data-source-manager.js?v=20250127:1001 === BiDataSourceManager - 数据获取完成 ===
bi-dashboard-designer.js?v=20250731-export-debug:10044  数据获取失败: undefined
updateWidgetData @ bi-dashboard-designer.js?v=20250731-export-debug:10044
await in updateWidgetData
updateWidgetDataSafe @ bi-dashboard-designer.js?v=20250731-export-debug:12871
waitForChartInit @ bi-dashboard-designer.js?v=20250731-export-debug:12787
setTimeout
setupWidgetAutoRefresh @ bi-dashboard-designer.js?v=20250731-export-debug:12792
applyPropertiesRealTime @ bi-dashboard-designer.js?v=20250731-export-debug:4824
onchange @ design:1
